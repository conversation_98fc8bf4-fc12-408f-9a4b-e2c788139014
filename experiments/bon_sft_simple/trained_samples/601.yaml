prompt: 'Question: <PERSON> has 5 times more money than <PERSON>. Together, they have
  $150. How much money, in dollars, does <PERSON> have?

  Answer: Let x be the amount of money <PERSON> has.

  <PERSON> has 5*x dollars.

  5*x+x=150

  6*x=150

  x=<<25=25>>25

  <PERSON> has 25*5=<<25*5=125>>125 dollars.

  #### 125


  Question: <PERSON> wants to lose 10 pounds by June. He lost 3 pounds in March and
  4 pounds in April. How much weight does he have to lose in May to meet his goal?

  Answer: In March and April, he lost 3 + 4 = <<3+4=7>>7 pounds.

  To meet his goal, he needs to lose 10 – 7 = <<10-7=3>>3 pounds.

  #### 3


  Question: <PERSON> loves playing video games.  His parents get him a console along
  with 5 games for his birthday.  He saves up enough money to buy 1 game per month
  for a year, and then the following year he starts buying 2 games a month.  For the
  third year he buys 4 games a month as he has a new part-time job that makes him
  more money.  He also gets 5 games for Christmas every year.  How many games does
  <PERSON> have after 3 years?

  Answer:'
question: <PERSON> loves playing video games.  His parents get him a console along with
  5 games for his birthday.  He saves up enough money to buy 1 game per month for
  a year, and then the following year he starts buying 2 games a month.  For the third
  year he buys 4 games a month as he has a new part-time job that makes him more money.  He
  also gets 5 games for Christmas every year.  How many games does <PERSON> have after
  3 years?
samples:
- ' He has 5 games initially.  Then he buys 12 games the first year.  Then he buys
  12 games the second year and 24 games the third year.  He gets 5 games for Christmas
  each year.  That makes 12 + 12 + 24 + 5 + 5 + 5 = 63 games.

  #### 63


  '
- ' He gets 5 games initially and then 5 games each year as gifts.

  He gets 12 + 12 + 12 = <<12+12+12=36>>36 games in the first two years.

  He gets 24 + 24 = <<24+24=48>>48 games in the third year.

  He gets 5 + 5 + 5 = <<5+5+5=15>>15 games in the first three years.

  He gets 5 games each year for gifts.

  He gets 12 + 12 + 12 = <<12+12+12=36>>36 games in the first two years.

  He gets 24 + 24 = <<24+24=48>>48 games in the third year.

  He gets 5 + 5 + 5 = <<5+5+5=15>>15 games in the first three years.

  #### 61


  '
- ' Steve gets 5 games for his birthday.

  Then for the first year, he gets 1 game per month and 5 games for Christmas, for
  a total of 12 + 5 = <<12+5=17>>17 games.

  For the second year, he gets 1 game per month and 5 games for Christmas, for a total
  of 12 + 5 = <<12+5=17>>17 games.

  For the third year, he gets 2 games per month and 5 games for Christmas, for a total
  of 24 + 5 = <<24+5=29>>29 games.

  For the first year, he has 5 games initially, and he buys 12 games. So he has 5
  + 12 = <<5+12=17>>17 games at the end of the year.

  For the second year, he has 17 games initially, and he buys 17 games. So he has
  17 + 17 = <<17+17=34>>34 games at the end of the year.

  For the third year, he has 34 games initially, and he buys 29 games. So he has 34
  + 29 = <<34+29=63>>63 games at the end of the year.

  #### 63


  '
- ' He has 5 games given to him at his birthday.

  1 game/month * 12 months/year * 2 years = <<1*12*2=24>>24 games

  2 games/month * 12 months/year * 1 year = <<2*12*1=24>>24 games

  4 games/month * 12 months/year * 1 year = <<4*12*1=48>>48 games

  5 games/year * 3 years = <<5*3=15>>15 games

  Adding all the games together, we have

  <<5+24+24+48+15=116>>116 games.

  #### 116


  '
- ' Initially, he has 5 games. Then for each year he has: <<5+1*12+2*12+4*12+5>>=5+12+24+48+5=94
  games.

  #### 94


  '
- ' Steve received 5 games for his birthday and then one game each month for a year.
  So, he received 5 + 12 = <<5+12=17>>17 games in the first year.

  In the second year, he bought 2 games each month. So, he received 2 * 12 = <<2*12=24>>24
  games in the second year.

  In the third year, he bought 4 games each month. So, he received 4 * 12 = <<4*12=48>>48
  games in the third year.

  In the first year, he received 5 games for Christmas. In the second and third year,
  he also received 5 games for Christmas each year. So, he received 5 + 5 + 5 = <<5+5+5=15>>15
  games for Christmas.

  Steve has received 17 + 24 + 48 + 15 = <<17+24+48+15=104>>104 games in total. ####
  104


  '
- ' Steve gets 5 games initially and then 12 games per year for 3 years, for a total
  of 12 * 3 = <<12*3=36>>36 games. He gets 5 more games each year for Christmas, for
  a total of 5 * 3 = <<5*3=15>>15 games. He has a total of 5 + 36 + 15 = <<5+36+15=56>>56
  games.

  #### 56


  '
- ' In the first year, Steve gets 12 + 5 = <<12+5=17>>17 games.

  In the second year, he gets 12 + 5 = <<12+5=17>>17 games.

  In the third year, he gets 12 + 5 = <<12+5=17>>17 games.

  He gets 17 + 17 + 17 = <<17+17+17=51>>51 games in total.

  #### 51


  '
- ' Steve gets 5 games for his birthday.

  Then he gets 5 games for each of the next 3 years.

  Then he gets 1 game for each of the next 12 months and 2 games for each of the next
  12 months.

  Then he gets 2 games for each of the next 12 months and 4 games for each of the
  next 12 months.

  Then he gets 5 games for Christmas every year.

  So Steve has 5 + 5 * 3 + 1 * 12 + 2 * 12 + 4 * 12 + 5 * 3 = <<5+5*3+1*12+2*12+4*12+5*3=5+15+12+24+48+15=119>>119
  games.

  #### 119


  '
- ' Steve gets 5 games for his birthday, and 5 games for Christmas each year.

  Steve gets 12 games in the first year, 12 games in the second year, and 12 games
  in the third year.

  Steve gets 1 game each month for the first year, 2 games each month for the second
  year, and 4 games each month for the third year.

  Steve gets 1 + 2 + 4 = <<1+2+4=7>>7 games each month. 12 months a year.

  Steve gets 7 * 12 = <<7*12=84>>84 games in the 3 years.

  Steve gets 5 + 5 + 5 + 5 = <<5+5+5+5=20>>20 games in Christmas.

  Steve gets 5 games for his birthday.

  Steve has 5 + 20 + 84 = <<5+20+84=109>>109 games.

  #### 109


  '
gt_answer: 'Steve buys 1 game a month his first year, and since there are 12 months
  in a year that means he buys 12*1=<<1*12=12>>12 games

  Steve then buys 2 games per month for a year, and since there are 12 months in a
  year that means he buys 12*2=<<2*12=24>>24 games

  Steve then buys 4 games per month for a year, and since there are 12 months in a
  year that means he buys 12*4=<<4*12=48>>48 games

  Steve also gets 5 games for Christmas each year for three years, for another 5*3=<<5*3=15>>15
  games.

  In total over three years, Steve gets 12+24+48+15=<<12+24+48+15=99>>99 games

  Since Steve started with 5 games, he has 5+99=<<5+99=104>>104 games after 3 years

  #### 104'
