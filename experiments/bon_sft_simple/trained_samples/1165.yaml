prompt: 'Question: Eliza can iron a blouse in 15 minutes and a dress in 20 minutes.  If
  she spends 2 hours ironing blouses and 3 hours ironing dresses, how many pieces
  of clothes did she iron?

  Answer: Since 60 minutes equal 1 hour, in 2 hours, Eliza ironed blouses for 2 x
  60 = <<2*60=120>>120 minutes.

  Eliza can iron 120 / 15 = <<120/15=8>>8 blouses.

  Since 60 minutes equal 1 hour, in 3 hours, Eliza ironed dresses for 3 x 60 = <<3*60=180>>180
  minutes.

  Eliza can iron 180 / 20 = <<180/20=9>>9 dresses.

  In total, Eliza ironed 8 + 9 = <<8+9=17>>17 pieces of clothes.

  #### 17


  Question: <PERSON> has 30 stickers. <PERSON> has thrice as many stickers as <PERSON>.   <PERSON>
  has 20 more stickers than <PERSON>. How many stickers do they have altogether?

  Answer: <PERSON> has 3 x 30 = <<3*30=90>>90 stickers.

  And, <PERSON> has 90 + 20 = <<90+20=110>>110 stickers.

  Therefore, they have 30 +90 + 110 = <<30+90+110=230>>230 stickers altogether.

  #### 230


  Question: <PERSON> collects garbage from 3 different apartment complexes.  The first
  two have 200 apartments each and the last one is 60% bigger than the other two combined.  He
  collects garbage 3 times a week from each place and he gets paid $.40 per collection
  for each apartment.  How much money does he make in a week?

  Answer:'
question: <PERSON> collects garbage from 3 different apartment complexes.  The first two
  have 200 apartments each and the last one is 60% bigger than the other two combined.  He
  collects garbage 3 times a week from each place and he gets paid $.40 per collection
  for each apartment.  How much money does he make in a week?
samples:
- ' The first two complexes have 200 apartments each. 200 + 200 = <<200+200=400>>400
  apartments.  The last complex is 60% bigger than the first two combined. 60% of
  400 is 0.6 * 400 = <<0.6*400=240>>240. 240 is the increase.  So the last complex
  has 400 + 240 = <<400+240=640>>640 apartments.

  In total, the three complexes have 200 + 200 + 640 = <<200+200+640=1040>>1040 apartments.

  John collects garbage 3 times a week from each place.  So he collects garbage 3
  * 1040 = <<3*1040=3120>>3120 times a week.  He gets paid $.40 for each collection
  for each apartment.  $.40 * 3120 = <<0.40*3120=1248>>1248.

  #### 1248


  '
- ' 200 x 2 = <<200*2=400>>400 apartments are in the first two complexes.  The last
  complex has 60% more than 400, so it has 400 * 1.6 = <<400*1.6=640>>640 apartments.  In
  total, John collects from 400 + 640 = <<400+640=1040>>1040 apartments.

  John collects garbage 3 times a week, so he collects from 1040 * 3 = <<1040*3=3120>>3120
  apartments in a week.

  He gets paid $.40 for each apartment, so he earns 3120 * .4 = <<3120*.4=1248>>1248
  dollars in a week.

  #### 1248


  '
- ' The first two apartment complexes have 200 x 2 = <<200*2=400>>400 apartments combined.  The
  last complex has 400 * 1.6 = <<400*1.6=640>>640 apartments.  In total, the three
  complexes have 400 + 640 = <<400+640=1040>>1040 apartments.  John collects garbage
  3 times a week from each place, so he collects garbage 3 * 1040 = <<3*1040=3120>>3120
  times a week.  He gets paid 3120 * .40 = <<3120*.40=1248>>1248 dollars a week.

  #### 1248


  '
- ' The first two apartment complexes have 200 + 200 = <<200+200=400>>400 apartments.

  The third apartment complex has 60% bigger apartments than the first two combined.  The
  third apartment complex has 400 * 1.6 = <<400*1.6=640>>640 apartments.

  John collects garbage 3 times a week from each place.  So, in total, he collects
  garbage 3 x (400 + 400 + 640) = <<3*(400+400+640)=3*(1400)=4200>>4200 times.

  He gets paid $.40 per collection for each apartment.  He makes 4200 * .4 = <<4200*.4=1680>>1680
  dollars.

  #### 1680


  '
- ' The first two have 200 + 200 = <<200+200=400>>400 apartments.

  The last one is 60% bigger than the other two combined, which is 400 * 1.6 = <<400*1.6=640>>640.  The
  total number of apartments is 400 + 640 = <<400+640=1040>>1040.  John gets paid
  $.40 per collection for each apartment.  Therefore, he gets paid $.40 * 1040 = <<.40*1040=416>>416
  per collection.

  Since he collects garbage 3 times a week, he gets paid 3 * 416 = <<3*416=1248>>1248.

  #### 1248


  '
- ' John collects from 200 + 200 = <<200+200=400>>400 apartments for the first two
  complexes.

  The last complex is 60% bigger than 400, which is 1.60 * 400 = <<1.6*400=640>>640.

  In total, John collects from 400 + 640 = <<400+640=1040>>1040 apartments.

  He collects three times a week, so he collects from 1040 * 3 = <<1040*3=3120>>3120
  apartments in a week.

  He gets paid $.40 for each apartment, so he makes 3120 * .40 = <<3120*.4=1248>>1248.

  #### 1248


  '
- ' John collects from 2 apartment complexes with 200 apartments each, so he collects
  2 x 200 = <<2*200=400>>400 times per week. He also collects from the last one, which
  is 60% bigger than the other two combined. The other two combined are 400 apartments,
  so the last one is 60% bigger than 400, or 60/100 * 400 = <<60/100*400=240>>240
  bigger than 400. So he collects 400 + 240 = <<400+240=640>>640 times per week. In
  total, he collects 400 + 640 = <<400+640=1040>>1040 times per week.  He gets paid
  $.40 per collection, so he makes 1040 x .40 = <<1040*.40=416>>416 per week.

  #### 416


  '
- ' The first two complexes have 200 x 2 = <<200*2=400>>400 apartments combined.

  The third complex has 400 * 1.6 = <<400*1.6=640>>640 apartments.

  So, John collects garbage from 400 + 640 = <<400+640=1040>>1040 apartments.

  He collects 1040 * 3 = <<1040*3=3120>>3120 pieces of garbage in a week.

  John gets paid 3120 * 0.40 = <<3120*0.4=1248>>1248 dollars a week.

  #### 1248


  '
- ' The first two apartment complexes have 2 x 200 = <<2*200=400>>400 apartments.
  The third complex has 60% more than 400 apartments. 60% of 400 is 0.6 x 400 = <<0.6*400=240>>240.
  So the third complex has 400 + 240 = <<400+240=640>>640 apartments. The total number
  of apartments is 400 + 400 + 640 = <<400+400+640=1440>>1440. John collects garbage
  3 times a week, so he collects 3 x 1440 = <<3*1440=4320>>4320 times a week. He gets
  paid $0.40 per collection, so he earns 0.4 x 4320 = <<0.4*4320=1728>>1728 dollars
  a week.

  #### 1728


  '
- ' John collects garbage 3 times a week from each place.  He has 3 places and collects
  garbage 3 times a week for each.  So, he collects garbage 3 * 3 = <<3*3=9>>9 times
  a week.

  First two complexes have 200 apartments each.  So, 2 * 200 = <<2*200=400>>400 apartments.

  Last one is 60% bigger than the other two combined.  So, it is 400 + 400 * 0.6 =
  <<400+400*0.6=400+240=640>>640.

  So, in total, John collects from 400 + 640 = <<400+640=1040>>1040 apartments.

  He collects garbage 9 times a week from each place.  So, he collects from each apartment
  9 times.  He collects from 1040 * 9 = <<1040*9=9360>>9360 apartments in total.

  He gets paid $.40 per collection for each apartment.  So, he gets paid 9360 * 0.4
  = <<9360*0.4=3744>>3744 in a week.

  #### 3744


  '
gt_answer: 'The first two complexes have 200+200=<<200+200=400>>400 apartments

  The third one has 400*.6=<<400*.6=240>>240 more apartments than those 2 combined

  So in total, it has 400+240=<<400+240=640>>640 apartments

  So he goes to 640+400=<<640+400=1040>>1040 apartments each time

  That means he visits 1040*3=<<1040*3=3120>>3120 apartments every week

  So he makes 3120*$0.4=$<<3120*0.4=1248>>1248 every week

  #### 1248'
