prompt: 'Question: <PERSON><PERSON> is planning the lunch menu for an elementary school. There
  are 5 third grade classes with 30 students each, 4 fourth grade classes with 28
  students each, and 4 fifth grade classes with 27 students each. Each student gets
  a hamburger, which costs $2.10, some carrots, which cost $0.50, and a cookie, which
  cost $0.20. How much does one lunch for all the students cost?

  Answer: First find the total number of students in third grade 5 classes * 30 students/class
  = <<5*30=150>>150 students

  Then find the total number of students in fourth grade 4 classes * 28 students/class
  = <<4*28=112>>112 students

  Then find the total number of student in fifth grade 4 classes * 27 students/class
  = <<4*27=108>>108 students

  Now add the number of students in each grade to find the total number of students
  150 students + 112 students + 108 students = <<150+112+108=370>>370 students

  Now find the total cost of one lunch $2.10 + $0.50 + $0.20 = $<<2.10+0.50+0.20=2.80>>2.80

  Now multiply that cost by the number of students to find the total cost for the
  school $2.80/student * 370 students = $<<2.80*370=1036>>1036

  #### 1036


  Question: <PERSON><PERSON> is a third of <PERSON><PERSON>’s age and <PERSON><PERSON> is a quarter of <PERSON><PERSON>’s
  age. <PERSON><PERSON> is twice <PERSON>’s age and <PERSON><PERSON><PERSON> is a third of <PERSON>’s age. <PERSON> is
  10 years older than <PERSON><PERSON><PERSON> but 2 years younger than <PERSON> who is 22 years old.
  How old, in years, is <PERSON><PERSON>?

  Answer: <PERSON> is younger than <PERSON> by 2 years, so he is 22 years old – 2 years
  = <<22-2=20>>20 years old.

  <PERSON><PERSON><PERSON> is 10 years younger than <PERSON> so she is 20 years old – 10 years = 10 years
  old.

  <PERSON> is 3 times older than <PERSON><PERSON><PERSON> so he is 10 years old * 3 = <<10*3=30>>30 years
  old.

  <PERSON><PERSON> is twice <PERSON>’s age so he is 30 years old * 2 = <<30*2=60>>60 years old.

  Amilia is a quarter of Yorick’s age so she is 60 years old / 4 = <<60/4=15>>15 years
  old.

  Tricia is a third of Amilia’s age so she is 15 years old / 3 = <<15/3=5>>5 years
  old.

  #### 5


  Question: A farm has 10 2-legged animals and 15 4-legged animals. If all animals
  are healthy, how many pairs of animal legs in total are on the farm?

  Answer:'
question: A farm has 10 2-legged animals and 15 4-legged animals. If all animals are
  healthy, how many pairs of animal legs in total are on the farm?
samples:
- ' The farm has 10 2-legged animals, which are 10 * 2 = <<10*2=20>>20 legs.

  The farm has 15 4-legged animals, which are 15 * 4 = <<15*4=60>>60 legs.

  In total there are 20 legs + 60 legs = <<20+60=80>>80 legs.

  #### 80


  '
- ' There are 10 2-legged animals and 15 4-legged animals. The number of 2-legged
  animals is 10 and the number of 4-legged animals is 15.

  The total number of legs is (10 * 2) + (15 * 4) = 20 + 60 = 80

  #### 80


  '
- ' 10 2-legged animals = <<10*2=20>>20 legs

  15 4-legged animals = <<15*4=60>>60 legs

  In total there are 20 legs + 60 legs = <<20+60=80>>80 legs

  #### 80


  '
- ' There are 10 2-legged animals, so there are 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals, so there are 15 * 4 = <<15*4=60>>60 legs.

  So there are 20 + 60 = <<20+60=80>>80 legs in total.

  #### 80


  '
- ' The total number of legs on the farm is (10 x 2) + (15 x 4) = <<10*2+15*4=20+60=80>>80.

  There are 80 legs in total.

  #### 80


  '
- ' There are 10 2-legged animals so there are 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals so there are 15 * 4 = <<15*4=60>>60 legs.

  There are 20 + 60 = <<20+60=80>>80 legs.

  #### 80


  '
- ' There are 10 2-legged animals so there are 10 * 2 = <<10*2=20>>20 legs for the
  2-legged animals.

  There are 15 4-legged animals so there are 15 * 4 = <<15*4=60>>60 legs for the 4-legged
  animals.

  There are 20 legs + 60 legs = <<20+60=80>>80 legs on the farm.

  #### 80


  '
- ' There are 10 2-legged animals, so they have 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals, so they have 15 * 4 = <<15*4=60>>60 legs.

  There are 20 + 60 = <<20+60=80>>80 legs in total.

  #### 80


  '
- ' There are 10 2-legged animals, so that is 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals, so that is 15 * 4 = <<15*4=60>>60 legs.

  Add the legs together to find the total 20 legs + 60 legs = <<20+60=80>>80 legs

  #### 80


  '
- ' We know there are 10 2-legged animals and 15 4-legged animals. 2-legged animals
  have 2 legs, so there are 2*10 = <<2*10=20>>20 legs. 4-legged animals have 4 legs,
  so there are 4*15 = <<4*15=60>>60 legs. Total legs are 20 legs + 60 legs = <<20+60=80>>80
  legs.

  #### 80


  '
gt_answer: '2-legged animals have 2 legs. Therefore 10 2-legged animals will have
  10*2=<<10*2=20>>20 legs

  4-legged animals have 4 legs. Therefore 15 4-legged animals will have 15*4=<<15*4=60>>60
  legs

  In total there are 20+60= <<20+60=80>>80 legs

  A pair of legs equals 2 legs. If there are 80 legs on the farm, there will be 80/2=
  <<80/2=40>>40 pairs of legs

  #### 40'
