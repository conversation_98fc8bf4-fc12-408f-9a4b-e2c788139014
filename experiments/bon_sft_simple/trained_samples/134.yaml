prompt: 'Question: <PERSON> has 54 pieces of gum, <PERSON> has 45 pieces of gum and <PERSON>
  has no pieces of gum. They decide to share the gum equally between the 3 of them.
  How many pieces of gum will each one get?

  Answer: Total pieces of gum are 54 pieces + 45 pieces + 0 pieces = <<54+45=99>>99
  pieces

  When the pieces are shared equally amongst 3 people, each one gets 99 pieces / 3
  people = <<99/3=33>>33 pieces/person

  #### 33


  Question: Three days'' temperatures were recorded for Bucyrus, Ohio. The temperatures
  were -14 degrees Fahrenheit, -8 degrees Fahrenheit, and +1 degree Fahrenheit. What
  was the average number of degrees (Fahrenheit) in Bucyrus, Ohio on the 3 days recorded?

  Answer: -14 + (-8) + 1 = <<-14+-8+1=-21>>-21 degrees Fahrenheit

  -21/3 = <<-21/3=-7>>-7 degrees Fahrenheit

  The average temperature was -7 degrees Fahrenheit.

  #### -7


  Question: <PERSON> gets 4 car washes a month.  If each car wash costs $15 how much does
  he pay in a year?

  Answer:'
question: <PERSON> gets 4 car washes a month.  If each car wash costs $15 how much does
  he pay in a year?
samples:
- ' <PERSON> gets 4 car washes per month. So in a year, <PERSON> gets 4 * 12 = 48 car washes

  48 * 15 = 720

  Tom pays $720 per year.

  #### $720


  '
- ' <PERSON> gets 4 car washes/month

  Tom gets 4 * 12 = <<4*12=48>>48 car washes/year

  Each car wash costs $15

  Tom pays 48 * 15 = <<48*15=720>>$720/year

  #### $720


  '
- ' Tom gets 4 car washes a month. So, in a year, he gets 12 months * 4 car washes/month
  = <<12*4=48>>48 car washes

  The cost per car wash is $15. So, the total cost of car washes in a year is 48 car
  washes * $15/car wash = <<48*15=720>>720 dollars

  #### 720


  '
- ' Tom gets 4 car washes a month and there are 12 months in a year. So Tom gets 12
  * 4 = 48 car washes a year. Each car wash costs $15. So he pays 48 * $15 = $720
  a year.

  #### $720


  '
- ' 4 car washes/month * 12 months/year = 48 car washes/year

  48 car washes/year * $15/car wash = $720/year

  Tom pays $720/year.

  #### $720


  '
- ' Tom gets 4 car washes a month and there are 12 months in a year, so he gets 4
  * 12 = <<4*12=48>>48 car washes in a year

  The cost of car washes in a year = 48 * $15 = <<48*15=720>>$720

  Tom pays $720 in a year for car washes.

  #### $720


  '
- ' If he gets 4 car washes a month and there are 12 months in a year, then he gets
  4 * 12 = <<4*12=48>>48 car washes

  Each car wash costs $15

  So total cost = 48 * $15 = <<48*15=720>>$720

  #### $720


  '
- ' <<4 car washes/month * 12 months/year = 48 car washes/year>>48 car washes/year

  48 car washes/year * $15/car wash = <<48*15=720>>720

  Tom pays $720 per year for car washes.

  #### $720


  '
- ' There are 12 months in a year. 12 x 4 = 48 car washes

  48 x $15 = $720

  #### $720


  '
- ' Each car wash is $15. In a month, he gets 4 car washes. So, for a month, it costs
  4 * $15 = $60. In a year, there are 12 months. So, in a year, it costs 12 * $60
  = $720.

  #### $720


  '
gt_answer: 'He gets 4*12=<<4*12=48>>48 car washes a year

  That means it cost 48*15=$<<48*15=720>>720

  #### 720'
