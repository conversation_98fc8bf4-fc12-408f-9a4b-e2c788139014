prompt: 'Question: Susan is picking strawberries. She''s trying to fill her basket,
  but out of every handful of 5 strawberries she can''t help but eat one of them herself.
  If her basket holds 60 strawberries, how many berries will she actually pick before
  she fills it?

  Answer: Out of every 5 berries, she eats 1, so only 5 strawberries - 1 strawberry
  = <<5-1=4>>4 strawberries go into the basket for each handful.

  The basket holds 60 strawberries, so it will take 60 strawberries / 4 strawberries
  per handful = <<60/4=15>>15 handfuls to fill the basket.

  So in total, <PERSON> will pick 60 strawberries + 15 handfuls * 1 strawberry eaten
  per handful = <<60+15*1=75>>75 strawberries.

  #### 75


  Question: <PERSON> and <PERSON> went to the canteen to buy snacks. They spent the same amount.
  <PERSON> bought a can of soda at $1 and two hamburgers at $2 each.  <PERSON> ordered two
  sandwiches for $3 and a can of fruit drink.  How much did <PERSON>''s fruit drink cost?

  Answer: Two hamburger cost 2 x $2 = $<<2*2=4>>4.

  So, <PERSON> spent $1 + $4 = $<<1+4=5>>5.

  Therefore, <PERSON>''s fruit drink cost $5 - $3 = $<<5-3=2>>2.

  #### 2


  Question: There are 90 rooms at the KozyInn Motel. It takes housekeeping 20 minutes
  to clean each room. How many hours would it take to clean one-half of the rooms?

  Answer:'
question: There are 90 rooms at the KozyInn Motel. It takes housekeeping 20 minutes
  to clean each room. How many hours would it take to clean one-half of the rooms?
samples:
- ' One-half of the rooms is 90 / 2 = <<90/2=45>>45.

  It takes housekeeping 20 minutes per room, so it will take 20 minutes * 45 rooms
  = <<20*45=900>>900 minutes.

  To convert minutes to hours, divide by 60: <<900/60=15>>15 hours.

  #### 15


  '
- ' If there are 90 rooms, then one-half of the rooms are 90/2=45 rooms.

  If it takes 20 minutes to clean each room, it would take 20 minutes x 45 rooms =
  <<20*45=900>>900 minutes to clean the rooms.

  900 minutes / 60 minutes per hour = <<900/60=15>>15 hours.

  #### 15


  '
- ' Cleaning half the rooms would take housekeeping to clean 90/2=45 rooms.

  Each room takes 20 minutes to clean, so cleaning 45 rooms would take 45 * 20 = <<45*20=900>>900
  minutes.

  #### 900


  '
- ' 45 rooms will be cleaned (half of 90).

  45 rooms x 20 minutes = 900 minutes

  900 minutes / 60 minutes = 15 hours

  #### 15


  '
- ' To clean one-half of the rooms, they have to clean 90 / 2 = 45 rooms.

  It takes them 20 minutes to clean each room, so it will take them 45 rooms * 20
  minutes per room = <<45*20=900>>900 minutes to clean the rooms.

  900 minutes / 60 minutes per hour = <<900/60=15>>15 hours.

  #### 15


  '
- ' It takes 20 minutes to clean one room.

  To clean half of the rooms, it will take half of 20 minutes = 10 minutes.

  There are 90 rooms, so half of the rooms = 90/2 = 45 rooms.

  45 rooms * 10 minutes/room = 450 minutes.

  450 minutes/60 minutes/hour = 7.5 hours.

  #### 7.5


  '
- ' Cleaning one half of 90 rooms is 90/2 = 45 rooms. Cleaning 45 rooms takes 45 *
  20/60 hours = 15 hours.

  #### 15


  '
- ' It takes 20 minutes to clean each room. To clean 1/2 of the rooms, it takes 20
  * 45 minutes = <<20*45=900>>900 minutes.

  #### 15


  '
- ' 20 minutes * 1/2 * 90 = 20 * 45 = 900 minutes = 900 / 60 = 15 hours.

  #### 15


  '
- ' It will take 20 minutes * 45 rooms = <<20*45=900>>900 minutes to clean half the
  rooms.

  900 minutes / 60 minutes per hour = <<900/60=15>>15 hours.

  #### 15


  '
gt_answer: '90 * 20 = <<90*20=1800>>1800 minutes

  1800/2 = <<1800/2=900>>900 minutes

  900 minutes = <<900/60=15>>15 hours

  It would take 15 hours to clean 45 rooms at the KozyInn Motel.

  #### 15'
