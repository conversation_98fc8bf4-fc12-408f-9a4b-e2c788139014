prompt: 'Question: <PERSON> was given 40 chocolate eggs for Easter. She likes to eat
  two each day after school. If <PERSON> has two chocolate eggs after school each day,
  how many weeks will they last?

  Answer: <PERSON> is eating 2 eggs each day after school and there are 5 school days
  in a week, so she eats 2 x 5 = <<2*5=10>>10 eggs each week.

  Since <PERSON> has 40 eggs / 10 eggs each week, they will last 4 weeks.

  #### 4


  Question: <PERSON> swims for 2 hours at a speed of 2 miles per hour.  He then runs for
  half the time at 4 times the speed.  How many miles did he cover?

  Answer: He swam 2*2=<<2*2=4>>4 miles

  He ran for 2/2=<<2/2=1>>1 hour

  His running speed was 2*4=<<2*4=8>>8 mph

  So he ran 8*1=<<8*1=8>>8 miles

  So his total distance was 8+4=<<8+4=12>>12 miles

  #### 12


  Question: <PERSON> has 20 animals on his farm, all either chickens or cows.
  They have a total of 70 legs, all together. How many of the animals are chickens?

  Answer:'
question: <PERSON> has 20 animals on his farm, all either chickens or cows. They
  have a total of 70 legs, all together. How many of the animals are chickens?
samples:
- ' Let x be the number of chickens. Then 20-x is the number of cows. Since each chicken
  has 2 legs and each cow has 4 legs, the total number of legs is x*2+(20-x)*4

  70=x*2+(20-x)*4

  70=2x+80-4x

  70=2x+80-4x

  70=2x+80-4x

  -10=-2x

  5=x

  #### 5


  '
- ' If there are 20 animals total and 70 legs, then we can solve for the number of
  chickens:

  70 legs / 4 legs per animal = <<70/4=17.5>>17.5 animals

  The other 20 - 17.5 = <<20-17.5=2.5>>2.5 animals are cows

  #### 17.5


  '
- ' Let x be the number of chickens. The number of cows is 20-x.

  The total number of legs is x+20-x=70.

  So, the number of legs is 70, which is 20 for the cows and x for the chickens.  So
  x=<<x=50>>50.

  #### 50


  '
- ' Let C be the number of chickens, and let C be the number of cows.

  We know that C + C = 20

  We know that 2*C + 2*C = 70

  We can solve the system of equations

  C + C = 20

  2*C + 2*C = 70

  C = 10

  #### 10


  '
- ' Farmer Brown has 20 animals on his farm, all either chickens or cows. They have
  a total of 70 legs, all together.

  Let c be the number of chickens on the farm.

  Let c be the number of cows on the farm.

  Then c + c = 20

  Then 2c = 20

  Then c = 10

  Then c + c = 20

  Then c = 10

  Then c = 10

  #### 10


  '
- ' Let''s say there are c chickens and 20-c cows.  Each chicken has 2 legs, so there
  are 2c chicken legs.  Each cow has 4 legs, so there are 4(20-c) cow legs.

  We know that the total number of legs is 70, so we can set up the equation:

  2c+4(20-c)=70

  Simplifying this equation, we get:

  2c+80-4c=70

  -2c=-10

  c=5

  #### 5


  '
- ' If we let c be the number of chickens and let C be the number of cows, we have

  c + C = 20

  and

  c + C legs = 70

  Let''s subtract the first equation from the second:

  c + C - c - C = 70 - 20

  c - C = 50

  c + C = 20

  2c = 70

  c = 35

  #### 35


  '
- ' Since there are 70 legs, and each animal has 4 legs, then there are 70/4=<<70/4=17.5>>17.5
  animals.

  But there are only 20 animals. So the number of animals is 20. We know that the
  number of animals is 20. So the number of animals is 20.

  #### 12


  '
- ' Let $c$ be the number of chickens. Then the number of cows is $20-c$, and the
  number of legs is $4c+6(20-c)=70$. Solving the equation $4c+6(20-c)=70$ for $c$
  yields $c=\boxed{10}.$

  #### 10


  '
- ' Let x be the number of chickens. Then 20-x are the number of cows.

  So the number of legs of the chickens is x, and the number of legs of the cows is
  20-x.

  So we have

  x+(20-x)=70

  x+20-x=70

  20=70

  20=70

  #### 10


  '
gt_answer: 'Let C be the number of chickens.

  There are 20-C cows.

  The cows have 4*(20-C) legs.

  The cows have 2C legs.

  The total number of legs is 2C+4(20-C)=70.

  2C+80-4C=70

  2C=10

  C=<<5=5>>5

  #### 5'
