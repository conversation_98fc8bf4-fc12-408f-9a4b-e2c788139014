prompt: 'Question: Erica is telling her grandson about the animals she saw during
  her recent safari in Kenya''s Maasai Mara. On Saturday, she saw 3 lions and 2 elephants.
  She spotted 2 buffaloes and 5 leopards on Sunday, and 5 rhinos and 3 warthogs on
  Monday. What is the total number of animals that <PERSON> saw?

  Answer: On Saturday, Erica saw 3 + 2 = <<3+2=5>>5 animals

  On Sunday she spotted 2 + 5 = <<2+5=7>>7 animals

  On Monday, she saw 5 + 3 = <<5+3=8>>8 animals

  Adding up all the animals <PERSON> saw gives a total of 5 + 7 + 8 = <<5+7+8=20>>20
  animals

  #### 20


  Question: A couple agreed to split their expenses equally, including the salary
  of their house help. While doing her job, the house help had an accident; thus,
  she underwent a medical procedure which cost her $128. The couple decided to pay
  half of the medical expenses and the other half will be deducted from her $160 salary.
  If the husband paid for the entire half of the hospital bill, how much does he still
  need to pay so that they split the expenses for the house help equally?

  Answer: The amount that the couple would cover for the hospital bill is $128/2 =
  $<<128/2=64>>64.

  The couple''s total expenses (salary and hospital bill) is $160 + $64 = $<<160+64=224>>224.

  So each of them should give $224/2 = $<<224/2=112>>112 in total.

  Since the husband paid the entire half of the hospital bill already ($64), he still
  needs to pay $112 - $64 = $<<48=48>>48.

  #### 48


  Question: <PERSON> goes to the market and buys 3 goats for $500 each and 2 cows for
  $1500 each.  How much money did he spend?

  Answer:'
question: <PERSON> goes to the market and buys 3 goats for $500 each and 2 cows for $1500
  each.  How much money did he spend?
samples:
- ' First, find the total money spent on goats: 3 x $<<3x500=1500>>1500 = $1500.

  Next, find the total money spent on cows: 2 x $<<2x1500=3000>>3000 = $3000.

  Adding up the money spent on goats and cows gives the total money spent: $1500 +
  $3000 = <<1500+3000=4500>>4500.

  #### 4500


  '
- ' The total cost of the goats is $500 x 3 = $<<500*3=1500>>1500.

  The total cost of the cows is $1500 x 2 = $<<1500*2=3000>>3000.

  Adding the costs of the goats and the cows, John spent $1500 + $3000 = $<<1500+3000=4500>>4500.

  #### 4500


  '
- ' For the 3 goats, John spent 3 x $500 = $<<3*500=1500>>1500.

  For the 2 cows, John spent 2 x $1500 = $<<2*1500=3000>>3000.

  Adding these costs together gives John''s total cost of $1500 + $3000 = $<<1500+3000=4500>>4500.

  #### 4500


  '
- ' The total cost of the goats is 3 * $500 = $<<3*500=1500>>1500.

  The total cost of the cows is 2 * $1500 = $<<2*1500=3000>>3000.

  The total cost of all the animals is $1500 + $3000 = $<<1500+3000=4500>>4500.

  #### 4500


  '
- ' John spent $500 * 3 = <<500*3=1500>>1500 dollars on goats and $1500 * 2 = <<1500*2=3000>>3000
  dollars on cows. Adding these expenses gives a total of 1500 + 3000 = <<1500+3000=4500>>4500
  dollars.

  #### 4500


  '
- ' 3 goats at $500 each will cost him $500*3 = $<<500*3=1500>>1500

  2 cows at $1500 each will cost him $1500*2 = $<<1500*2=3000>>3000

  Total cost is $1500 + $3000 = $<<1500+3000=4500>>4500.

  #### 4500


  '
- ' To find the total amount of money spent, we can add the costs of the goats and
  the cows.

  The total cost of the goats is 3 * $<<500=1500>>1500 = $<<3*500=1500>>1500.

  The total cost of the cows is 2 * $<<1500=3000>>3000 = $<<2*1500=3000>>3000.

  Adding the costs of the goats and the cows gives a total of $<<1500+3000=4500>>4500.

  #### 4500


  '
- ' John spent $500 for the first goat, $500 for the second goat, $500 for the third
  goat, $1500 for the first cow, and $1500 for the second cow.  Adding up all the
  amounts gives a total of $500 + $500 + $500 + $1500 + $1500 = $<<500+500+500+1500+1500=3500>>3500.

  #### 3500


  '
- ' The total cost of the goats is $500 * 3 = $<<500*3=1500>>1500.

  The total cost of the cows is $1500 * 2 = $<<1500*2=3000>>3000.

  Adding up the total cost of the goats and the cows, John spent $1500 + $3000 = $<<1500+3000=4500>>4500.

  #### 4500


  '
- ' He spent $500 * 3 = <<500*3=1500>>1500 for the goats

  He spent $1500 * 2 = <<1500*2=3000>>3000 for the cows

  In total, he spent 1500 + 3000 = <<1500+3000=4500>>4500

  #### 4500


  '
gt_answer: 'The goats cost 3*500=$<<3*500=1500>>1500

  The cows cost 2*1500=$<<2*1500=3000>>3000

  So in total he spends 1500+3000=$<<1500+3000=4500>>4500

  #### 4500'
