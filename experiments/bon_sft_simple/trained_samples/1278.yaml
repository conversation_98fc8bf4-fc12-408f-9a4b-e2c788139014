prompt: 'Question: The sum of the ages of <PERSON>, <PERSON> and <PERSON> in three years
  is 150. Currently, <PERSON> is 4 years older than <PERSON>. If <PERSON>''s age is 40,
  calculate <PERSON>''s age three years from now?

  Answer: The age of <PERSON> three years from now will be 40+3 = <<40+3=43>>43 years

  Since <PERSON> age is four years more than <PERSON>''s, <PERSON> is currently 40+4
  = 44 years.

  In three years, <PERSON> will be 44+3 = <<44+3=47>>47 years old

  The total age of <PERSON> and <PERSON> in three years will be 47+43 = <<47+43=90>>90

  If the total age of <PERSON>, <PERSON> and <PERSON> in three years is 150, then <PERSON>''s
  age in three years will be 150-90 = <<150-90=60>>60

  #### 60


  Question: <PERSON><PERSON> has three times the number of counters and four times the number
  of marbles as <PERSON>. If <PERSON> has 40 counters and 50 marbles, calculate the number
  of counters and marbles that <PERSON><PERSON> has?

  Answer: If <PERSON> has 40 counters, and <PERSON><PERSON> has three, the number of counters <PERSON>
  has, <PERSON><PERSON> has 3*40 = <<40*3=120>>120 counters.

  <PERSON><PERSON> also has four times the number of marbles that <PERSON> has, meaning he has 4*50
  = <<4*50=200>>200 marbles.

  In total, <PERSON><PERSON> has 120 counters + 200 marbles = <<120+200=320>>320 items.

  #### 320


  Question: <PERSON> found 214 seashells, <PERSON> found 432 seashells, and <PERSON> found 86
  seashells on the beach. When they cleaned them, they discovered that 67 were cracked.
  How many good seashells did they find together?

  Answer:'
question: <PERSON> found 214 seashells, <PERSON> found 432 seashells, and <PERSON> found 86 seashells
  on the beach. When they cleaned them, they discovered that 67 were cracked. How
  many good seashells did they find together?
samples:
- ' To find the number of good seashells, we need to subtract the number of cracked
  seashells from the total number of seashells.

  <PERSON> has 214 seashells, <PERSON> has 432 seashells, and <PERSON> has 86 seashells, so the
  total number of seashells is 214+432+86 = <<214+432+86=732>>732

  Of these, 67 are cracked, so the number of good seashells is 732-67 = <<732-67=665>>665

  #### 665


  '
- ' 214+432+86 = <<214+432+86=732>>732 shells

  After cleaning 67 shells, there will be 732 - 67 = <<732-67=665>>665 shells.

  #### 665


  '
- ' The total number of seashells they found is 214 + 432 + 86 = <<214+432+86=732>>732.

  67 of these were cracked. So the number of good seashells is 732 - 67 = <<732-67=665>>665.

  #### 665


  '
- ' The total number of seashells they found is 214+432+86 = <<214+432+86=732>>732.

  67 of these seashells are cracked, so 732-67 = <<732-67=665>>665 seashells are good.

  #### 665


  '
- ' The total number of seashells that they found is 214+432+86 = <<214+432+86=732>>732

  Of these, 67 were cracked. The number of good seashells is 732-67 = <<732-67=665>>665

  #### 665


  '
- ' 214+432+86 = <<214+432+86=732>>732

  After cleaning, 67 were cracked. So the total number of good seashells will be the
  total number of seashells found minus the number of cracked seashells: 732-67 =
  <<732-67=665>>665.

  #### 665


  '
- ' Tom found 214 seashells, Nancy found 432 seashells, and Benny found 86 seashells.
  In total, they found 214 + 432 + 86 = <<214+432+86=732>>732 seashells.

  After cleaning, they found that 67 seashells were cracked. So they have 732 - 67
  = <<732-67=665>>665 good seashells.

  #### 665


  '
- ' If Tom found 214 seashells, Nancy found 432 seashells, and Benny found 86 seashells,
  then they found 214+432+86 = <<214+432+86=732>>732 seashells in total.

  If 67 of them were cracked, then they had 732-67 = <<732-67=665>>665 good seashells.

  #### 665


  '
- ' The total number of seashells found by Tom, Nancy and Benny is 214+432+86 = <<214+432+86=732>>732

  After cleaning, the number of good seashells is the total number of seashells minus
  the number of cracked seashells.

  So the number of good seashells is 732-67 = <<732-67=665>>665

  #### 665


  '
- ' First, we have to find how many seashells they found in total. Tom found 214,
  Nancy found 432, and Benny found 86, so the total is 214+432+86 = <<214+432+86=732>>732
  seashells.

  Now, we have to subtract the number of cracked seashells from the total number of
  seashells. The number of cracked seashells is 67. 732 - 67 = <<732-67=665>>665

  #### 665


  '
gt_answer: 'Together they found 214 + 432 + 86 = <<214+432+86=732>>732 seashells before
  cleaning.

  After cleaning, their final seashell is 732 - 67 = <<732-67=665>>665.

  #### 665'
