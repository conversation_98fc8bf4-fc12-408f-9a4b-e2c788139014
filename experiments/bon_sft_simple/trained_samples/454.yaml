prompt: 'Question: <PERSON> and his brother <PERSON> bought presents for their father.
  <PERSON> bought a wallet at $50 and two pairs of sneakers at $100 each pair, while
  <PERSON> bought a backpack at $100 and two pairs of jeans at $50 each pair. How much
  did they spend in all?

  Answer: Two pairs of sneakers cost 2 x $100 = $<<2*100=200>>200.

  So, <PERSON> spent $50 + $200 = $<<50+200=250>>250.

  Two pairs of jeans cost 2 x $50 = $<<2*50=100>>100.

  Thus, <PERSON> spent $100 + $100 = $<<100+100=200>>200.

  Therefore, they spent a total of $250 + $200 = $<<250+200=450>>450 in all.

  #### 450


  Question: <PERSON> bought 7 packs of red bouncy balls and 6 packs of yellow bouncy balls.
  Each pack contained 18 bouncy balls. How many more red bouncy balls than yellow
  bouncy balls did Kate buy?

  Answer: <PERSON> bought 7 packs × 18 balls/pack = <<7*18=126>>126 red bouncy balls.

  She also bought 6 packs × 18 balls/pack = <<6*18=108>>108 yellow bouncy balls.

  <PERSON> bought 126 balls - 108 balls= <<126-108=18>>18 more red bouncy balls than yellow
  bouncy balls.

  #### 18


  Question: <PERSON> and his neighbor <PERSON> each eat 4 apples a day. How many apples
  do they eat in 30 days?

  Answer:'
question: <PERSON> and his neighbor <PERSON> each eat 4 apples a day. How many apples do
  they eat in 30 days?
samples:
- ' <PERSON> eats 4 apples/day, so in 30 days he eats 4 x 30 = <<4*30=120>>120 apples.

  <PERSON> also eats 4 apples/day, so in 30 days she eats 4 x 30 = <<4*30=120>>120 apples.

  In total, they eat 120 apples + 120 apples = <<120+120=240>>240 apples.

  #### 240


  '
- ' <PERSON> eats 4 apples per day × 30 days = <<4*30=120>>120 apples.

  Nancy also eats 4 apples per day × 30 days = <<4*30=120>>120 apples.

  Together, they eat 120 apples + 120 apples = <<120+120=240>>240 apples.

  #### 240


  '
- ' Marin eats 4 apples a day.

  So, in 30 days, he eats 4 apples/day × 30 days= <<4*30=120>>120 apples.

  Nancy eats 4 apples a day.

  So, in 30 days, she eats 4 apples/day × 30 days= <<4*30=120>>120 apples.

  Marin and Nancy eat 120 apples + 120 apples= <<120+120=240>>240 apples in 30 days.

  #### 240


  '
- ' They eat 4 apples a day.

  So, in 30 days, they eat 4 apples/day × 30 days = <<4*30=120>>120 apples.

  #### 120


  '
- ' Marin eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  Nancy eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  Marin and Nancy eat 120 apples + 120 apples = <<120+120=240>>240 apples in total.

  #### 240


  '
- ' Marin eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  Nancy eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  In total, they eat 120 apples + 120 apples = <<120+120=240>>240 apples.

  #### 240


  '
- ' Marin eats 4 apples × 30 days = <<4*30=120>>120 apples.

  Nancy eats 4 apples × 30 days = <<4*30=120>>120 apples.

  So, Marin and Nancy together eat 120 apples + 120 apples = <<120+120=240>>240 apples.

  #### 240


  '
- ' Marin eats 4 apples/day × 30 days = <<4*30=120>>120 apples in 30 days.

  Nancy eats 4 apples/day × 30 days = <<4*30=120>>120 apples in 30 days.

  Therefore, Marin and Nancy eat a total of 120 apples + 120 apples = <<120+120=240>>240
  apples in 30 days.

  #### 240


  '
- ' Marin eats 4 apples/day × 30 days = <<4*30=120>>120 apples in 30 days.

  Nancy eats 4 apples/day × 30 days = <<4*30=120>>120 apples in 30 days.

  Marin and Nancy eat 120 apples + 120 apples = <<120+120=240>>240 apples in 30 days.

  #### 240


  '
- ' Marin eats 4 apples × 30 days = <<4*30=120>>120 apples.

  Nancy eats 4 apples × 30 days = <<4*30=120>>120 apples.

  So, they eat 120 + 120 = <<120+120=240>>240 apples.

  #### 240


  '
gt_answer: 'In one day, Marin and Nancy eat 4 + 1 = <<4+1=5>>5 apples.

  In 30 days, they eat 30 * 5 = <<30*5=150>>150 apples.

  #### 150'
