prompt: 'Question: <PERSON> is saving his money to buy a brand new bike that costs $100.  His
  weekly allowance is $5.  His parent will pay him an extra $10 to mow the lawn.  His
  neighbor will pay him $7 per hour to babysit their son.  He has already saved up
  $65.  He''ll receive his allowance on Friday and he''s planning on babysitting for
  2 hours this Saturday after he mows the lawn.  How much more money does <PERSON> need
  to earn before he can buy the bike?

  Answer: If he babysits for 2 hours at $7 per hour, he will earn 2*7 = $<<2*7=14>>14

  This week he will earn $5 allowance, $10 mowing the lawn and $14 from babysitting
  for a total of 5+10+14 = $<<5+10+14=29>>29

  If we add the $29 he will earn to his $65 savings, he will have a total of 29 +
  65 = $<<29+65=94>>94

  The bike costs $100 and he will have $94 leaving $100-$94 = $<<100-94=6>>6 more
  that he will need to earn

  #### 6


  Question: <PERSON> and <PERSON><PERSON> both went to the same middle school. However, when they
  finished college, <PERSON> had five more degrees than <PERSON><PERSON>. If <PERSON> has a total
  of 150 degrees, what''s the combined number of degrees they both have?

  Answer: <PERSON><PERSON> has 150 - 5 = <<150-5=145>>145 degrees

  Since <PERSON> has 150 degrees, the combined total for both is 150+145 = <<150+145=295>>295

  #### 295


  Question: <PERSON> is picking up the toys on the floor of his room.  He''d dumped
  a lego boxed set with 500 pieces on the floor, and another one that had 3 times
  more pieces than the 500 piece one, and another one that had 1/4 the number of pieces.  How
  many blocks does <PERSON> pick up if he picks up all the legos?

  Answer:'
question: <PERSON> is picking up the toys on the floor of his room.  He'd dumped a lego
  boxed set with 500 pieces on the floor, and another one that had 3 times more pieces
  than the 500 piece one, and another one that had 1/4 the number of pieces.  How
  many blocks does Johnny pick up if he picks up all the legos?
samples:
- ' The other box set had 500 * 3 = <<500*3=1500>>1500 pieces

  The other box set had 500 / 4 = <<500/4=125>>125 pieces

  Adding the three boxes together we get 500 + 1500 + 125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' The 500 piece set had 500 pieces

  The 3 times the 500 piece set has 3*500 = <<3*500=1500>>1500 pieces

  The 1/4 the 500 piece set has 1/4 * 500 = <<1/4*500=125>>125 pieces

  The total is 500 + 1500 + 125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' The first box has 500 blocks. The second box has 500*3 = <<500*3=1500>>1500 blocks.
  The third box has 500/4 = <<500/4=125>>125 blocks. The total number of blocks is
  500+1500+125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' The first box had 500 pieces.  The second box had 3 times the pieces of the first,
  so 500*3 = <<500*3=1500>>1500

  The third box had 1/4 the number of pieces of the first, so 500/4 = <<500/4=125>>125

  Adding them all up, he picks up 500 + 1500 + 125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' The first box had 500 pieces.  The second box has 500*3 = <<500*3=1500>>1500 pieces.  The
  third box has 500/4 = <<500/4=125>>125 pieces.  So in total, the number of blocks
  he picks up is 500+1500+125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' There are 500 pieces in the first box. There are 500*3 = 1500 pieces in the second
  box and 500/4 = 125 pieces in the third box.  So the total number of pieces is 500
  + 1500 + 125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' If we let x equal the number of pieces in the 3 times as big lego box, we can
  set up the following equation: 3*500 + 500 + (500/4) = x

  If we simplify the equation we get 3*500+500+(500/4) = x

  3*500+500+125 = x

  3*500+625 = x

  1500+625 = x

  <<1500+625=2125>>2125

  #### 2125


  '
- ' The second box had 500*3 = <<500*3=1500>>1500 pieces.  The third box had 500/4
  = <<500/4=125>>125 pieces

  If we add the 500, 1500, and 125 we get 500+1500+125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' The second set has 3 times as many pieces as the 500 piece set.  This is 500*3
  = <<500*3=1500>>1500

  The 3rd set has 1/4 the number of pieces as the 500 piece set.  This is 500/4 =
  <<500/4=125>>125

  The total number of pieces is 500 + 1500 + 125 = <<500+1500+125=2125>>2125

  #### 2125


  '
- ' A 500 piece set has 500 pieces. A 3 times bigger set has 500*3 = <<500*3=1500>>1500
  pieces.  A 1/4 the size set has 1/4*500 = <<1/4*500=125>>125

  The total number of blocks is 500+1500+125 = <<500+1500+125=2125>>2125

  #### 2125


  '
gt_answer: 'The first set had 500 pieces, and the second set had 3 times as many as
  the first set, so the second set has 500*3=<<500*3=1500>>1500 pieces

  The third set had one-quarter of the 500 pieces, so that means it had 500*.25= <<500*.25=125>>125
  pieces

  In total, that means Johnny picked up 500 + 1500+ 125= <<500+1500+125=2125>>2125
  blocks

  #### 2,125'
