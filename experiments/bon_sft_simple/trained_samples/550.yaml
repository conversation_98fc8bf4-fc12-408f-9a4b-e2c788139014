prompt: 'Question: <PERSON> has a small tree outside his window with 30 branches with
  90 twigs per branch. 30% of the twigs sprout 4 leaves and the rest sprout 5 leaves.
  How many leaves are on the tree total?

  Answer: First find the total number of twigs on the tree: 30 branches * 90 twigs/branch
  = <<30*90=2700>>2700 twigs

  Then multiply that number by 30% to find how many twigs have 4 leaves: 2700 twigs
  * 30% = <<2700*30*.01=810>>810 twigs

  Then subtract that number from the total number of twigs to find how many have 5
  leaves: 2700 twigs - 810 twigs = <<2700-810=1890>>1890 twigs

  Then multiply that number by 5 to find the number of leaves on the 5-leaved twigs:
  1890 twigs * 5 leaves/twig = <<1890*5=9450>>9450 leaves

  Then multiply the number of 4-leaved twigs by 4 to find the total number of leaves
  they have: 810 twigs * 4 leaves/twig = <<810*4=3240>>3240 leaves

  Then add the two groups of leaves to find the total number of leaves: 9450 leaves
  + 3240 leaves = <<9450+3240=12690>>12690 leaves

  #### 12690


  Question: In the matrix, there are seven fewer noodles than pirates. If there are
  45 pirates, how many noodles and pirates are there in total?

  Answer: In the matrix, there are seven fewer noodles than pirates, meaning there
  are 45-7=<<45-7=38>>38 noodles.

  The number of noodles and pirates in the matrix is 38+45=<<38+45=83>>83

  #### 83


  Question: A shop sells school supplies. One notebook is sold at $1.50 each, a pen
  at $0.25 each, a calculator at $12 each, and a geometry set at $10. Daniel is an
  engineering student, and he wants to buy five notebooks, two pens, one calculator,
  and one geometry set. The shop gives a 10% discount on all the purchased items.
  How much does Daniel have to spend on all the items he wants to buy?

  Answer:'
question: A shop sells school supplies. One notebook is sold at $1.50 each, a pen
  at $0.25 each, a calculator at $12 each, and a geometry set at $10. Daniel is an
  engineering student, and he wants to buy five notebooks, two pens, one calculator,
  and one geometry set. The shop gives a 10% discount on all the purchased items.
  How much does Daniel have to spend on all the items he wants to buy?
samples:
- ' Calculate the cost of the items Daniel wants to buy before the discount: 5 notebooks
  * $1.50/notebook = <<5*1.5=7.50>>$7.50 + 2 pens * $0.25/pen = <<2*.25=0.50>>$0.50
  + 1 calculator * $12/calculator = <<1*12=12>>$12 + 1 geometry set * $10/geometry
  set = <<1*10=10>>$10

  The total cost of the items is $7.50 + $0.50 + $12 + $10 = <<7.5+.5+12+10=30>>$30

  The discount is 10% of the total cost, so the discount is $3.

  The total amount Daniel has to pay is the cost minus the discount, which is $30-$3=<<30-3=27>>$27

  #### 27


  '
- ' First find the cost of each item: 5 notebooks at $1.50 each = <<5*1.50=7.50>>7.50
  dollars

  2 pens at $0.25 each = <<2*0.25=0.50>>0.50 dollars

  1 calculator at $12 each = <<1*12=12>>12 dollars

  1 geometry set at $10 each = <<1*10=10>>10 dollars

  Total cost is 7.50+0.50+12+10=<<7.50+0.50+12+10=30>>30 dollars

  Then find the discount: 10% of the total cost is 10%*30=<<10*.01*30=3>>3 dollars

  Then subtract the discount from the total cost to find the cost after the discount:
  30-3=<<30-3=27>>27 dollars

  #### 27


  '
- ' The total price of the items Daniel wants to buy is: 5 notebooks * $1.50 = $7.50,
  2 pens * $0.25 = $0.50, 1 calculator * $12 = $12, 1 geometry set * $10 = $10.

  The total price before discount is $7.50 + $0.50 + $12 + $10 = <<7.50+0.50+12+10=30>>$30

  A 10% discount is $30 * 0.10 = <<30*0.10=3>>$3

  So, Daniel has to spend $30 - $3 = <<30-3=27>>$27

  #### 27


  '
- ' The total cost of the items Daniel wants to buy is 5*1.5+2*0.25+12+10=<<5*1.5+2*0.25+12+10=35.5>>35.5
  dollars

  Then multiply that by 10% to find the discount: 35.5*10%=<<35.5*0.10=3.55>>3.55
  dollars

  Then subtract the discount from the total cost to find the cost of the items after
  the discount: 35.5-3.55=<<35.5-3.55=31.95>>31.95 dollars

  #### 31.95


  '
- ' The total cost of the items Daniel wants to buy is: 5 notebooks * $1.50/notebook
  = <<5*1.5=7.50>>7.50, 2 pens * $0.25/pen = <<2*.25=0.50>>0.50, 1 calculator * $12/calculator
  = <<1*12=12>>12, 1 geometry set * $10/geometry set = <<1*10=10>>10

  The total cost is $7.50 + $0.50 + $12 + $10 = <<7.50+0.50+12+10=30>>30

  Since the shop gives a 10% discount on all the purchased items, the discount is
  10% of $30 = <<30*.10=3>>3

  Then the total cost Daniel has to spend is $30 - $3 = <<30-3=27>>27

  #### 27


  '
- ' First, calculate the total cost of the items Daniel wants to buy: 5 notebooks
  * $1.50 = <<5*1.5=7.5>>7.50

  Then multiply that number by 10% to find the discount: 7.50 * 10% = <<7.5*.10=0.75>>0.75

  Then subtract the discount from the total cost to find the final cost: 7.50 - 0.75
  = <<7.5-.75=6.75>>6.75

  Then add the cost of the other items to the final cost: 6.75 + 2*0.25 = <<6.75+0.5=7.25>>7.25

  Then add the cost of the other items to the final cost: 7.25 + 12 + 10 = <<7.25+12+10=29.25>>29.25

  #### 29.25


  '
- ' Daniel wants to buy five notebooks, two pens, one calculator, and one geometry
  set. He needs to buy 5 notebooks for <<5*1.50=7.50>>7.50, 2 pens for <<2*0.25=0.50>>0.50,
  1 calculator for <<1*12=12>>12, and 1 geometry set for <<1*10=10>>10. This gives
  him a total of <<7.50+0.50+12+10=30>>30

  Daniel gets a 10% discount on all items, meaning he will pay 90% of the total cost.
  This means he will pay <<30*0.90=27>>27

  #### 27


  '
- ' Calculate the total cost of the items he wants to buy: 5 notebooks * $1.50 = <<5*1.50=7.50>>$7.50,
  2 pens * $0.25 = <<2*.25=0.50>>$0.50, 1 calculator * $12 = <<1*12=12>>$12, 1 geometry
  set * $10 = <<1*10=10>>$10.

  Then add the cost of all the items: $7.50 + $0.50 + $12 + $10 = <<7.50+0.50+12+10=30>>$30.

  Then calculate the discount: 10% of $30 = <<30*.10=3>>$3.

  Then subtract the discount from the total cost: $30 - $3 = <<30-3=27>>$27.

  #### 27


  '
- ' Calculate the total cost of Daniel''s items without the discount: 5 notebooks
  * 1.50/notebook = <<5*1.5=7.50>>7.50, 2 pens * 0.25/pen = <<2*0.25=0.50>>0.50, 1
  calculator * 12/calculator = <<1*12=12>>12, 1 geometry set * 10/geometry set = <<1*10=10>>10

  Then add the costs together: 7.50 + 0.50 + 12 + 10 = <<7.50+0.50+12+10=30>>30

  Then calculate the discount: 10% of the total cost = <<10%*30=3>>3

  Then subtract the discount from the total cost: 30 - 3 = <<30-3=27>>27

  #### 27


  '
- ' The total cost of the items Daniel wants to buy is:

  5 notebooks * $1.50/notebook = <<5*1.50=7.50>>$7.50

  2 pens * $0.25/pen = <<2*0.25=0.50>>$0.50

  1 calculator * $12/calculator = <<1*12=12>>$12

  1 geometry set * $10/geometry set = <<1*10=10>>$10

  The total cost is $7.50 + $0.50 + $12 + $10 = <<7.50+0.50+12+10=30>>$30

  The shop gives a 10% discount, so we need to calculate 10% of the total cost to
  get the discount: 10% = <<10*.01=0.10>>0.10

  Discount = $30 * 0.10 = <<30*0.10=3>>$3

  The total amount Daniel has to spend is the total cost minus the discount: $30 -
  $3 = <<30-3=27>>$27

  #### 27


  '
gt_answer: 'Five notebooks cost $5 x 1.50 = $<<5*1.50=7.50>>7.50

  Two pens cost 2 x $0.25 = $<<2*0.25=0.50>>0.50.

  The total cost is $7.50 + $0.50 + $12 + $10 =$<<7.5+0.5+12+10=30>>30.

  Thus, total discount is 10/100 x $30 = $<<10/100*30=3>>3.

  Therefore, Daniel has to spend $30 - $3 = $<<30-3=27>>27 on all the items he wants
  to buy.

  #### 27'
