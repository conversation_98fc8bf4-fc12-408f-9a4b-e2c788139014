gt_answer: 'Each boy gets $18/3 = $<<18/3=6>>6.

  The eldest had $5+$6=$<<5+6=11>>11 after adding $5 to his share.

  He added another $10 so he had $11+$10=$<<11+10=21>>21.

  Then he had $21-$8=$<<21-8=13>>13 after spending $8.

  So, he now has $13*3=$<<13*3=39>>39.

  #### 39'
is_corrects:
- true
- true
- true
- false
- false
- true
- true
- true
- true
- true
prompt: "Question: At Theo\u2019s cafe, he makes 3 egg and 4 egg omelettes.  His cafe\
  \ is open from 7:00 a.m. to 11:00 a.m.  In the first hour, 5 customers order the\
  \ 3 egg omelettes.  In the second hour, 7 customers order the 4 egg omelettes. \
  \ In the third hour, 3 customers order the 3 egg omelettes. In the last hour, 8\
  \ customers order the 4 egg omelettes.  How many eggs does <PERSON> need to make all\
  \ the omelettes?\nAnswer: In the first and third hour, 5 + 3 = <<5+3=8>>8 customers\
  \ order the 3 egg omelettes.\nIn the second and last hour, 7 + 8 = <<7+8=15>>15\
  \ customers order the 4 egg omelettes.\nTo make the 3 egg omelettes, <PERSON> needs\
  \ 3 x 8 = <<3*8=24>>24 eggs.\nTo make the 4 egg omelettes, <PERSON> needs 15 x 4 = <<15*4=60>>60\
  \ eggs.\nTo make all the omelettes, he needs 24 + 60 = <<24+60=84>>84 eggs.\n####\
  \ 84\n\nQuestion: Allison, a YouTuber, uploads 10 one-hour videos of food reviews\
  \ each day to her channel. She uploaded videos halfway through June,  at that pace,\
  \  and then doubled the number of video hours she uploaded on the remaining days.\
  \ What's the total number of video hours she has uploaded at the end of the month?\n\
  Answer: Since June has 30 days, halfway is 1/2*30 = <<1/2*30=15>>15\nThe total number\
  \ of video hours halfway through June is 10*15 = <<10*15=150>>150\nWhen she doubled\
  \ the number of video hours, the total per day became 10*2 = <<10*2=20>>20\nThe\
  \ total number of video hours for the remaining days is 15*20 = <<15*20=300>>300\n\
  The total number of video hours for the whole month is 300+150 = <<300+150=450>>450\n\
  #### 450\n\nQuestion: $18 is shared among 3 boys. The eldest added $5 to his share\
  \ and added another $10 the following day. He later needed some money and spent\
  \ $8 from his total so far. After saving some more, he was able to triple the amount\
  \ he had left after spending the $8. How much does he have now?\nAnswer:"
question: $18 is shared among 3 boys. The eldest added $5 to his share and added another
  $10 the following day. He later needed some money and spent $8 from his total so
  far. After saving some more, he was able to triple the amount he had left after
  spending the $8. How much does he have now?
samples:
- ' The eldest boy started with $18/3 = <<18/3=6>>6

  He added $5 and then $10. So he had 6+5+10 = <<6+5+10=21>>21

  He spent $8 and had 21-8 = <<21-8=13>>13

  He tripled the amount he had left after spending $8. So he had 3*13 = <<3*13=39>>39

  #### 39


  '
- ' The eldest boy got $18/3 = <<18/3=6>>6 initially.

  The eldest boy added $5 to his share and $10 the following day. So he added $5+10
  = <<5+10=15>>15 to his share.

  After adding $15, his total amount is 6+15 = <<6+15=21>>21

  He later spent $8 from his total amount. So he has 21-8 = <<21-8=13>>13 left.

  He was able to triple the amount he had left after spending $8. So he tripled his
  amount to 3*13 = <<3*13=39>>39

  #### 39


  '
- ' The eldest boy initially had $18 / 3 = <<18/3=6>>6.

  After adding $5 and $10, he had 6 + 5 + 10 = <<6+5+10=21>>21.

  After spending $8, he had 21 - 8 = <<21-8=13>>13.

  After tripling the amount, he had 3 * 13 = <<3*13=39>>39.

  #### 39


  '
- ' The eldest boy has $5 added to his share, so now he has 5+18/3 = <<5+18/3=9>>9
  dollars.

  He then added another $10, so now he has 9+10 = <<9+10=19>>19 dollars.

  After spending $8, he has 19-8 = <<19-8=11>>11 dollars.

  Then he triples his amount, so now he has 11*3 = <<11*3=33>>33 dollars.

  #### 33


  '
- ' He started with $5+15 = <<5+15=20>>20

  He spent $8 and has 20-8 = <<20-8=12>>12

  He tripled his share and has 12*3 = <<12*3=36>>36

  #### 36


  '
- ' The eldest boy initially received $18/3 = <<18/3=6>>6

  He added $5 to his share, so he had $6 + $5 = <<6+5=11>>11

  He added another $10, so he had $11 + $10 = <<11+10=21>>21

  He spent $8, so he had $21 - $8 = <<21-8=13>>13

  He tripled the amount he had left after spending $8, so he had 3*13 = <<3*13=39>>39

  #### 39


  '
- ' The eldest boy started with 18/3 = <<18/3=6>>6.

  He added 5 and then 10, so he had 6 + 5 + 10 = <<6+5+10=21>>21.

  He spent 8, so he had 21-8 = <<21-8=13>>13.

  He tripled this amount, so he had 3*13 = <<3*13=39>>39.

  #### 39


  '
- ' He had 18/3 = <<18/3=6>>6 dollars initially.

  He added 5 dollars to his share, so he now has 6+5 = <<6+5=11>>11 dollars.

  He added 10 dollars to his share, so he now has 11+10 = <<11+10=21>>21 dollars.

  He spent 8 dollars, so he now has 21-8 = <<21-8=13>>13 dollars.

  He tripled the amount he had left, so he now has 3*13 = <<3*13=39>>39 dollars.

  #### 39


  '
- ' First, we need to know how much money the eldest has at the beginning.

  The eldest boy had his share of 18 / 3 = <<18/3=6>>6 at the beginning.

  He added $5 and $10, so he had $6 + $5 + $10 = <<6+5+10=21>>21 at that point.

  He spent $8, so he had $21 - $8 = <<21-8=13>>13 left.

  After tripling that amount, he had 3 * 13 = <<3*13=39>>39.

  #### 39


  '
- ' The eldest boy started with $18/3 = <<18/3=6>>6.

  He then added $5 = <<5>>5 to his share.

  He added $10 = <<10>>10 the following day.

  He had $6+5+10 = <<6+5+10=21>>21.

  He then spent $8 = <<8>>8 from his total so far.

  He had $21-8 = <<21-8=13>>13.

  He tripled the amount he had left after spending the $8.

  He tripled $13 = <<3*13=39>>39.

  #### 39


  '
