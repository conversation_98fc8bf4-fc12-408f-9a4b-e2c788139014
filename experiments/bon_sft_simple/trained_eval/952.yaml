gt_answer: 'In one hour, there are 60 / 15 = <<60/15=4>>4 (15 minutes).

  From 8am - 11am, there are 11 - 8 = <<11-8=3>>3 hours.

  In 3 hours, there are 3 x 4 = <<3*4=12>>12 (15 minutes).

  In those 3 hours, <PERSON> stapled 12 x 30 = <<12*30=360>>360 reports.

  #### 360'
is_corrects:
- true
- false
- true
- true
- true
- true
- false
- false
- false
- false
prompt: 'Question: <PERSON> had 20 paperclips initially, but then lost 12. <PERSON> has 1/4
  more than what <PERSON> currently has, plus 7. How many paperclips does <PERSON> have?

  Answer: <PERSON> has 20-12= <<20-12=8>>8 paperclips.

  <PERSON> has 8/4 + 7 = <<8/4+7=9>>9 paperclips

  #### 9


  Question: <PERSON> has $204 in her piggy bank. She spent half the amount and bought
  a toy. She then spent half of the remaining money and bought her brother a book.
  How much money, in dollars, was left over?

  Answer: After buying the toy, <PERSON> has 204/2=<<204/2=102>>102 dollars left.

  <PERSON> has 102/2=<<102/2=51>>51 dollars left.

  #### 51


  Question: Vince can staple 30 reports every 15 minutes.  If he was stapling reports
  from 8:00 AM until 11:00 PM, how many reports did he staple altogether?

  Answer:'
question: <PERSON> can staple 30 reports every 15 minutes.  If he was stapling reports
  from 8:00 AM until 11:00 PM, how many reports did he staple altogether?
samples:
- ' 11:00 PM - 8:00 AM = <<11:00 PM - 8:00 AM = 3>>3 hours.

  In 1 hour, <PERSON> can staple 30/15=<<30/15=2>>2*60=<<2*60=120>>120 reports.

  In 3 hours, Vince can staple 3*120=<<3*120=360>>360 reports.

  #### 360


  '
- ' 9 hours * 60 minutes = 540 minutes

  Vince stapled reports for 540 minutes.

  He stapled reports for 540/15=<<540/15=36>>36 times in 15 minutes.

  He stapled 30*36=<<30*36=1080>>1080 reports.

  #### 1080


  '
- ' Vince stapled 30/15=<<30/15=2>>2 reports every minute.

  He stapled 2*60=<<2*60=120>>120 reports in an hour.

  He stapled 120*3=<<120*3=360>>360 reports in 3 hours.

  #### 360


  '
- ' Vince staples reports for 3 hours, or 3*60 = <<3*60=180>>180 minutes.

  He staples 30/15 = <<30/15=2>>2 reports per minute, so he staples 180*2 = <<180*2=360>>360
  reports

  #### 360


  '
- ' From 8:00 AM to 11:00 PM is 3 hours. Vince can staple reports for 15 minutes at
  a time. So Vince can staple reports for 4 * 3 = <<4*3=12>>12 times.

  Vince can staple 12 * 30 = <<12*30=360>>360 reports

  #### 360


  '
- ' Vince staples 30 reports every 15 minutes.

  He staples 30 * 4 = <<30*4=120>>120 reports every hour.

  He staples 120 * 3 = <<120*3=360>>360 reports in 3 hours.

  #### 360


  '
- ' 8:00 AM to 11:00 PM is 15 hours. Vince can staple 30/15=<<30/15=2>>2 reports per
  minute.

  #### 2


  '
- ' Vince can staple 30 reports every 15 minutes.

  So Vince can staple 30*2=<<30*2=60>>60 reports every 30 minutes.

  #### 60


  '
- ' Vince staples 30 reports every 15 minutes.

  #### 30/15=2


  He staples 30 * 2 reports per 15 minutes.

  #### 30*2=60


  He staples 60 reports per 15 minutes.

  #### 60*4=240


  He staples 60 * 4 reports per 60 minutes.

  #### 60*4=240


  He staples 60 * 4 reports per 60 minutes.

  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  #### 240


  ####'
- ' Vince stapled 30 reports in 15 minutes. So Vince stapled 30*2=<<30*2=60>>60 reports
  in 30 minutes.

  Vince stapled 60*8=<<60*8=480>>480 reports in 2 hours.

  Vince stapled 480*3=<<480*3=1440>>1440 reports in 6 hours.

  #### 1440


  '
