gt_answer: 'He can speak 150*2.5=<<150*2.5=375>>375 words per minute

  He wants to say 450*10=<<450*10=4500>>4500 words

  So he can speak them in 4500/375=<<4500/375=12>>12 minutes

  #### 12'
is_corrects:
- true
- true
- true
- true
- true
- false
- true
- true
- true
- true
prompt: 'Question: A box has 2 dozen water bottles and half a dozen more apple bottles
  than water bottles. How many bottles are in the box?

  Answer: Since a dozen has 12 items, the box has 2 dozen water bottles, a total of
  2*12=<<2*12=24>>24 water bottles.

  The box also has half a dozen more apple bottles than water bottles, meaning there
  are 1/2*12= 6 more apple bottles than water bottles.

  The total number of apple bottles in the box is 24+6 = <<24+6=30>>30

  In the box, there are a total of 30+24 =<<30+24=54>>54 bottles

  #### 54


  Question: A shop is offering a discount on pens. If you buy 10 pens at the regular
  price, you can get the next 10 for half off. A customer came in and bought 20 pens
  for $30. What''s the regular price of one pen in dollars?

  Answer: Let p be the cost of a full-priced pen.

  We know that the first 10 pens are sold at the regular price, so they cost 10*p.

  We also know the any pens purchased after the first 10 are half-price, so they cost
  (20 pens - 10 pens) * p/2 on those.

  If we add the cost of the first 10 pens and the pens sold at half-price it equals
  the total purchase of price of $30=10*p + (20-10)*p/2.

  Thus, $30 = 10*p + 10 * p/2.

  And, $30 = 10 *p + 5*p.

  So, $30 = 15* p.

  Finally, p=$<<2=2>>2.

  #### 2


  Question: <PERSON> decides to take up an odd hobby of speed talking.  His normally speaking
  speed is 150 WPM.  After training his speed is 2.5 times faster than his starting
  speed.  How long would it take him to speak 10 pages if each page has 450 words
  per page?

  Answer:'
question: John decides to take up an odd hobby of speed talking.  His normally speaking
  speed is 150 WPM.  After training his speed is 2.5 times faster than his starting
  speed.  How long would it take him to speak 10 pages if each page has 450 words
  per page?
samples:
- ' John''s normal speaking speed is 150 WPM.  After training his speed is 2.5 times
  faster than his starting speed.  So his new speed is 2.5 * 150 = 375 WPM.  Since
  each page has 450 words per page, and there are 10 pages, there are 450 * 10 = 4500
  words in total.  At his new speed of 375 words per minute, it will take him 4500/375
  minutes to finish speaking.  4500/375 is 12.

  #### 12


  '
- ' 150 WPM means he can say 150 words per minute.

  At this speed, it will take him 450 words / 150 words per minute = 3 minutes to
  say one page.

  If he is 2.5 times faster than his starting speed, that means his speed is 150 *
  2.5 = 375 words per minute.

  It will take him 450 words / 375 words per minute = 1.2 minutes to say one page.

  To say 10 pages will take him 1.2 * 10 = 12 minutes.

  #### 12


  '
- ' We know that John''s speaking speed has increased by 2.5 times. So it now is 2.5*150=375
  words per minute.

  We also know that each page has 450 words.

  Since we want to know how long it takes John to speak 10 pages, we can find the
  total number of words in 10 pages by multiplying 10 by the number of words per page.
  10*450=4500 words.

  We can then divide 4500 words by John''s new speed of 375 words per minute to find
  the time in minutes it takes John to speak 10 pages. 4500/375=12.

  #### 12


  '
- ' Speed of speaking is 150 WPM.

  After training, the speed of speaking is 2.5 times faster than his starting speed.

  2.5*150 = <<2.5*150=375>>375

  Speed of speaking after training is 375 WPM.

  Each page has 450 words per page.

  So total words in 10 pages is 450 * 10 =<<450*10=4500>>4500.

  Time taken to speak 4500 words is 4500/375 =<<4500/375=12>>12 minutes.

  #### 12


  '
- ' If John''s normal speed is 150 words per minute, then his speed after training
  is 2.5*150=375 words per minute.

  In a minute he can say 375 words.

  If each page has 450 words, then it would take him 450/375=<<450/375=1.2>>1.2 minutes
  per page.

  If there are 10 pages, then it would take him 10*1.2=<<10*1.2=12>>12 minutes.

  #### 12


  '
- ' John''s normally speaking speed is 150 WPM.  After training his speed is 2.5 times
  faster than his starting speed.  So his speaking speed is 2.5 * 150 = 375 words
  per minute.  If each page has 450 words per page, then there are 450/375 = 1.2 pages
  per minute.  So it would take him 10/1.2 minutes to speak 10 pages.

  #### 8.3


  '
- ' The speed talking of John is 2.5 times faster than his starting speed. So, his
  speed is 2.5 *150 = 375 WPM.

  10 pages have 450 words per page, so the total number of words is 10 * 450 = 4500
  words.

  4500 words have 4500/375 = 12 minutes to speak.

  #### 12


  '
- ' John''s speed is now 2.5 times faster than his starting speed. This means his
  new speed is 150*2.5= <<150*2.5=375>>375 words per minute.

  Since each page has 450 words, he can read 450/375 words in 1 minute.

  And, he can read 1 page in 450/375 minutes.

  And, he can read 10 pages in 450/375*10 minutes.

  450/375*10 = 12 minutes.

  #### 12


  '
- ' The normal speed of John is 150 WPM.  That means he can speak 150 words in one
  minute.

  After training, his speed is 2.5 times faster than his starting speed.  That means
  he can speak 150 * 2.5 = 375 words per minute.

  If each page has 450 words per page, then one page will take him 450 / 375 = 1.2
  minutes.

  So for 10 pages, he will take 10 * 1.2 = 12 minutes.

  #### 12


  '
- ' John''s starting speed was 150 WPM.

  After training, his speed became 2.5 times faster, so his new speed was 2.5 * 150
  = 375 WPM.

  To calculate the number of minutes per page, we divide the number of words per page
  by John''s speed in words per minute.

  The number of words per page is 450 words, and John''s speed in words per minute
  is 375 words per minute.

  The time per page is 450 / 375 = 1.2 minutes per page.

  There are 10 pages, so the total time would be 10 * 1.2 minutes = 12 minutes.

  #### 12


  '
