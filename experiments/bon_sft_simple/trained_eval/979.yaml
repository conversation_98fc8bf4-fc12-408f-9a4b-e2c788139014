gt_answer: 'His dad will be 24 x 2 = <<24*2=48>>48 years old when <PERSON> is 24.

  His brother will be 24 / 2 = <<24/2=12>>12 years old then.

  <PERSON> will turn 24 in 24 - 19 = <<24-19=5>>5 years.

  The age of his father now is 48 - 5= <<48-5=43>>43.

  His brother is 12 - 5 = <<12-5=7>>7 years old right now.

  The sum of his dad''s and his brother''s ages now is 43 + 7 = <<43+7=50>>50.

  #### 50'
is_corrects:
- false
- false
- false
- false
- false
- true
- true
- true
- false
- false
prompt: 'Question: <PERSON> and <PERSON> will celebrate their 20th wedding anniversary in
  2025.  They started dating 3 years before they got married and met 2 years before
  that.  When did they meet?

  Answer: They''ll celebrate their 20th wedding anniversary in 2025 which means they
  got married in 2025-20 = <<2025-20=2005>>2005

  They started dating 3 years before they got married in 2005 which means they started
  dating in 2005-3 = 2002

  And they met 2 years before they started dating which means they met in 2002-2 =
  2000

  #### 2000


  Question: <PERSON><PERSON> has 7 acorns. <PERSON> has 5 times as many acorns as <PERSON><PERSON>, but
  3 fewer acorns than <PERSON>. How many acorns do they have altogether?

  Answer: <PERSON> has 7 x 5 = <<7*5=35>>35 acorns.

  <PERSON> has 35 + 3 = <<35+3=38>>38 acorns.

  <PERSON>gether, they have 7 + 35 + 38 = <<7+35+38=80>>80 acorns

  #### 80


  Question: <PERSON> is 19 years old now. When he turns 24, he will be half the age of
  his dad but twice as old as his brother.  What is the sum of the ages of his dad
  and his brother now?

  Answer:'
question: <PERSON> is 19 years old now. When he turns 24, he will be half the age of his
  dad but twice as old as his brother.  What is the sum of the ages of his dad and
  his brother now?
samples:
- ' When Emil turns 24, he will be half the age of his dad.

  So his dad will be 24 x 2 = <<24*2=48>>48 years old.

  Emil will be twice as old as his brother when he turns 24, so his brother will be
  24 / 2 = <<24/2=12>>12 years old.

  Emil is now 19 years old, so his brother is 12-19 = <<12-19=-7>>-7 years old.  His
  dad is 48-19 = <<48-19=29>>29 years old.

  So, the sum of their ages now is -7 + 29 = <<-7+29=22>>22

  #### 22


  '
- ' If Emil turns 24, he''ll be 24 - 19 = <<24-19=5>>5 years older, which means he''ll
  be 24 years old.

  His dad will be twice as old which means he''ll be 24 x 2 = <<24*2=48>>48 years
  old.

  His brother will be half as old as his dad which means he''ll be 48/2 = <<48/2=24>>24
  years old.

  Their ages now are 48 - 5 = <<48-5=43>>43 and 24 - 5 = <<24-5=19>>19 years old.

  #### 43 + 19 = 62


  '
- ' When Emil turns 24, his dad will be 2 x 24 = <<2*24=48>>48 years old.

  His brother will be half of 48 which is 24 years old.

  Emil is 19 now, so he is 24-19 = <<24-19=5>>5 years younger than his brother.

  #### 48 + 19 = 67


  '
- ' When Emil turns 24, he will be half the age of his dad but twice as old as his
  brother.  So his dad will be 2 x 24 = 48 years old, and his brother will be 24/2
  = 12 years old.

  Since Emil is 19 years old now, his dad will be 48 - 19 = <<48-19=29>>29 years old
  now, and his brother will be 12 - 19 = <<12-19=-7>>7 years old now.  The sum of
  their ages is 29 + 7 = <<29+7=36>>36

  #### 36


  '
- ' Emil will be 24 years old when he turns 24. That means he is 24 - 19 = <<24-19=5>>5
  years away from turning 24.

  When Emil turns 24, his dad is 24 x 2 = <<24*2=48>>48 years old.

  Emil''s brother is half his dad''s age. Half of 48 is 48/2 = <<48/2=24>>24.

  Emil''s dad is 48 years old, and his brother is 24 years old. 5 years ago Emil was
  19 years old, and his dad was 48 - 5 = <<48-5=43>>43 years old.

  And his brother was 24 - 5 = <<24-5=19>>19 years old.

  The sum of their ages now is 43 + 19 = <<43+19=62>>62

  #### 62


  '
- ' If Emil is 19, he will be 24 in 5 years.  When he turns 24, his brother will be
  24/2 = 12 years old.  Emil''s dad will be 24 * 2 = 48 years old.

  So Emil''s dad is 48 - 5 = <<48-5=43>>43 now.

  Emil''s brother is 12 - 5 = <<12-5=7>>7 now.

  So their sum is 43 + 7 = <<43+7=50>>50

  #### 50


  '
- ' Emil''s age in 5 years will be 24.

  His dad''s age in 5 years will be 2 * 24 = 48.

  His brother''s age in 5 years will be 24 / 2 = 12.

  Emil''s age now is 19. His dad''s age now is 48 - 5 = 43. His brother''s age now
  is 12 - 5 = 7.

  The sum of the ages of his dad and his brother is 43 + 7 = <<43+7=50>>50.

  #### 50


  '
- ' Emil will be 24 years old when he turns 24.

  He will be twice as old as his brother at that time, so his brother will be 24/2
  = <<24/2=12>>12 years old at that time.

  His brother is 12 years younger than Emil now, so his brother is 19 - 12 = <<19-12=7>>7
  years old now.

  Emil will be half the age of his dad when he turns 24, so his dad will be 24 x 2
  = <<24*2=48>>48 years old when Emil turns 24.

  His dad is 48 - 24 + 19 = <<48-24+19=43>>43 years old now.

  The sum of their ages now is 7 + 43 = <<7+43=50>>50

  #### 50


  '
- ' Emil will be 24 years old when he turns 24, which means Emil''s dad will be 2*24=<<2*24=48>>48
  years old when Emil turns 24.

  Emil''s brother will be 24/2 = <<24/2=12>>12 years old when Emil turns 24.

  Emil''s dad is 48-19 = <<48-19=29>>29 years old now.

  His brother is 12-19 = <<12-19=-7>>-7 years old now.

  #### -7+29=22


  '
- ' Emil will be 24 years old when he turns 24, so he''ll be 24 - 19 = <<24-19=5>>5
  years older than he is now.

  When he turns 24, his brother will be half his age, which is 24 / 2 = <<24/2=12>>12.

  And his dad will be twice his age, which is 2 x 12 = <<2*12=24>>24.

  Since Emil''s dad will be 24 years old when Emil turns 24, his dad is currently
  24 - 5 = <<24-5=19>>19 years old.

  And his brother is currently 12 - 5 = <<12-5=7>>7 years old.

  The sum of their ages is 19 + 7 = <<19+7=26>>26.

  #### 26


  '
