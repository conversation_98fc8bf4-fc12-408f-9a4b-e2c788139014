gt_answer: '<PERSON> needs a total of 15 x 3 = <<15*3=45>>45 eggs.

  She will have to babysit 45/9 = <<45/9=5>>5 times.

  #### 5'
is_corrects:
- true
- true
- true
- true
- false
- true
- true
- true
- true
- true
prompt: "Question: <PERSON> is sitting outside, counting how many people go past him\
  \ in vehicles. He has counted 52 vehicles so far today. He saw 12 trucks, a couple\
  \ of buses, twice as many taxis, some motorbikes, and 30 cars. If the trucks held\
  \ 2 people each, the buses held 15 people each, the taxis held 2 people each, the\
  \ motorbikes held 1 person each, and the cars held 3 people each, how many passengers\
  \ has <PERSON> seen today?\nAnswer: In the trucks, <PERSON> saw 12 * 2 = <<12*2=24>>24\
  \ people.\nIn the buses, he saw 2 * 15 = <<2*15=30>>30 people.\nHe saw 2 * 2 = <<2*2=4>>4\
  \ taxis go past him.\nTherefore, he saw 4 * 2 = <<4*2=8>>8 people in the taxis.\n\
  In the cars, he saw 30 * 3 = <<30*3=90>>90 people.\nSince we know how many vehicles\
  \ <PERSON> saw in total, we know that he saw 52 \u2013 12 \u2013 2 \u2013 4 \u2013\
  \ 30 = <<52-12-2-4-30=4>>4 motorbikes.\nSo, he saw 4 * 1 = <<4*1=4>>4 people on\
  \ motorbikes.\nHe has therefore seen a total of 24 + 30 + 8 + 90 + 4 = <<24+30+8+90+4=156>>156\
  \ people.\n#### 156\n\nQuestion: The government donated tons of sand to four Cities.\
  \ City A received 16 1/2 tons of sand, City B received 26 tons of sand, and City\
  \ C received 24 1/2 tons of sand. The supplier lost the data for City D; however,\
  \ they know the total for all four cities was 95 tons. How many tons of sand did\
  \ City D receive?\nAnswer: The total tons of sand donated to City A and City C is\
  \ 16 1/2 + 24 1/2 = 41.\nThe total tons of sand donated to City A, City B, and City\
  \ C is 41 + 26 = <<41+26=67>>67.\nTherefore, City D received 95 - 67 = <<95-67=28>>28\
  \ tons of sand.\n#### 28\n\nQuestion: Sandra's neighbor gives her a basket of 9\
  \ eggs every time she babysits their daughter. To make a Spanish flan, she needs\
  \ 3 eggs. If Sandra has been tasked to make 15 Spanish flans for her school fundraiser,\
  \ how many times does Sandra have to babysit?\nAnswer:"
question: Sandra's neighbor gives her a basket of 9 eggs every time she babysits their
  daughter. To make a Spanish flan, she needs 3 eggs. If Sandra has been tasked to
  make 15 Spanish flans for her school fundraiser, how many times does Sandra have
  to babysit?
samples:
- ' To make 15 Spanish flans, Sandra needs 15 * 3 = <<15*3=45>>45 eggs.

  Sandra has to babysit enough times to have at least 45 eggs. 9 * 5 = <<9*5=45>>45,
  so Sandra must babysit at least 5 times.

  #### 5


  '
- ' In total, Sandra will need 15 * 3 = <<15*3=45>>45 eggs.

  She has 9 eggs in each basket. 45 / 9 = <<45/9=5>>5.

  #### 5


  '
- ' We know that Sandra needs 3 eggs per Spanish flan. Therefore, she needs 3 * 15
  = <<3*15=45>>45 eggs.

  She received 9 eggs per babysitting session. To get 45 eggs, she needs to babysit
  45 / 9 = <<45/9=5>>5 times.

  #### 5


  '
- ' To make 15 Spanish flans, she needs 15 * 3 = <<15*3=45>>45 eggs.

  If she gets 9 eggs each time, then she needs to babysit 45 / 9 = <<45/9=5>>5 times.

  #### 5


  '
- ' To make 15 Spanish flans, she needs 15 * 3 = <<15*3=45>>45 eggs.

  She already has 9 eggs. Therefore, she will need 45 - 9 = <<45-9=36>>36 more eggs.

  She will have to babysit 36 / 9 = <<36/9=4>>4 times.

  #### 4


  '
- ' Since she needs 3 eggs for each Spanish flan, she will need 15 * 3 = <<15*3=45>>45
  eggs.

  She will receive 9 eggs each time she babysits, so she will need to babysit 45/9
  = <<45/9=5>>5 times.

  #### 5


  '
- ' If she needs 3 eggs for each flan, she needs 3 * 15 = <<3*15=45>>45 eggs.

  She received 9 eggs each time she babysits, so she needs to babysit 45 / 9 = <<45/9=5>>5
  times.

  #### 5


  '
- ' In order to make a Spanish flan, Sandra needs 3 eggs. If she needs to make 15
  Spanish flans, she needs 3 * 15 = <<3*15=45>>45 eggs.

  If she receives 9 eggs every time she babysits, she will need 45 / 9 = <<45/9=5>>5
  times to babysit.

  #### 5


  '
- ' She needs 15 * 3 = <<15*3=45>>45 eggs for the flans.

  To get that many eggs, she needs to babysit 45 / 9 = <<45/9=5>>5 times.

  #### 5


  '
- ' To make 15 Spanish flans, she will need 15 * 3 = <<15*3=45>>45 eggs.

  Since she gets 9 eggs each time, she will need 45 / 9 = <<45/9=5>>5 times.

  #### 5


  '
