gt_answer: 'First find how many more widgets the Widgeteer 3000 makes per hour: 60
  widgets/hour - 45 widgets/hour = <<60-45=15>>15 widgets/hour

  Then multiply that difference by the number of hours in a day to find the daily
  difference: 15 widgets/hour * 24 hours/day = <<360=360>>360 widgets/day

  Then multiply the daily difference by the number of days in a month to find the
  monthly difference: 360 widgets/day * 30 days/month = <<360*30=10800>>10,800 widgets/month

  Then multiply that number by the cost per widget to find the total earnings difference:
  10,800 widgets/month * $6/widget = $<<10800*6=64800>>64,800

  #### 64800'
is_corrects:
- true
- true
- true
- false
- true
- true
- true
- true
- false
- true
prompt: 'Question: <PERSON><PERSON> has a pizza that is cut into 16 slices. After she and her
  friends finish eating, there are 4 slices left. If each of them ate 2 slices of
  pizza, how many people ate the pizza?

  Answer: 16 - 4 = <<16-4=12>>12 slices of pizza were eaten.

  So there were 12/2 = <<12/2=6>>6 people eating the pizza.

  #### 6


  Question: <PERSON> wants to fly to Australia. The regular tickets cost about $2000.
  As <PERSON> is a student, he will get a 30% discount on this price. How much does
  he need to pay for his ticket?

  Answer: The discount on the ticket is 30/100 * $2000 = $<<30/100*2000=600>>600.

  So <PERSON> needs to pay $2000 - $600 = $<<2000-600=1400>>1400.

  #### 1400


  Question: Fern is trying to decide between buying the Widgeteer 3000, which makes
  60 widgets an hour, and the WidgetMaster 9000, which makes 45 widgets an hour. Each
  widget sells for $6. How much more money will Fern make from the Widgeteer 3000
  vs. the WidgetMaster 9000 if it runs 24 hours a day for a month with 30 days?

  Answer:'
question: Fern is trying to decide between buying the Widgeteer 3000, which makes
  60 widgets an hour, and the WidgetMaster 9000, which makes 45 widgets an hour. Each
  widget sells for $6. How much more money will Fern make from the Widgeteer 3000
  vs. the WidgetMaster 9000 if it runs 24 hours a day for a month with 30 days?
samples:
- ' The Widgeteer 3000 makes 60 widgets per hour. In a day, it will make 60 * 24 =
  1440 widgets. In a month, it will make 1440 * 30 = 43200 widgets. It will make $6
  * 43200 = $<<6*43200=259200>>$259200.

  The WidgetMaster 9000 makes 45 widgets per hour. In a day, it will make 45 * 24
  = 1080 widgets. In a month, it will make 1080 * 30 = 32400 widgets. It will make
  $6 * 32400 = $<<6*32400=194400>>$194400.

  The difference between these is $259200 - $194400 = $<<259200-194400=64800>>$64800.

  #### 64800


  '
- ' The Widgeteer 3000 makes 60 * 24 * 30 = 43,200 widgets.

  The WidgetMaster 9000 makes 45 * 24 * 30 = 32,400 widgets.

  The Widgeteer 3000 makes 43,200 - 32,400 = <<43200-32400=10800>>10,800 more widgets.

  Each widget sells for $6, so Fern will make $10,800 * $6 = <<10800*6=64800>>$64,800
  more money from the Widgeteer 3000.

  #### 64800


  '
- ' In 1 hour, Widgeteer 3000 makes 60 widgets and WidgetMaster 9000 makes 45 widgets.
  So, in 1 hour, Widgeteer 3000 makes $60 * $6 = $<<60*6=360>>360 and WidgetMaster
  9000 makes $45 * $6 = $<<45*6=270>>270.

  In a day, Widgeteer 3000 makes $360 * 24 = $<<360*24=8640>>8640 and WidgetMaster
  9000 makes $270 * 24 = $<<270*24=6480>>6480.

  In a month, Widgeteer 3000 makes $8640 * 30 = $<<8640*30=259200>>259200 and WidgetMaster
  9000 makes $6480 * 30 = $<<6480*30=194400>>194400.

  The difference is $259200 - $194400 = $<<259200-194400=64800>>64800.

  #### 64800


  '
- ' First, let''s calculate the total number of widgets each machine can make in a
  month.

  Widgeteer 3000: 60 widgets/hr * 24 hours/day * 30 days = 43,200 widgets.

  WidgetMaster 9000: 45 widgets/hr * 24 hours/day * 30 days = 40,800 widgets.

  Now let''s calculate the total revenue from each machine.

  Widgeteer 3000: 43,200 widgets * $6/widget = $<<43200*6=259200>>259,200.

  WidgetMaster 9000: 40,800 widgets * $6/widget = $<<40800*6=245800>>245,800.

  The Widgeteer 3000 makes $259,200 - $245,800 = <<259200-245800=13500>>$13,500 more
  than the WidgetMaster 9000.

  #### 13500


  '
- ' Widgeteer 3000 makes 60 widgets per hour, so in a day it makes 60 * 24 = 1440
  widgets. In a month it makes 1440 * 30 = 43200 widgets. It sells these for $6 each,
  so it earns $43200 * $6 = $<<43200*6=259200>>259200.

  WidgetMaster 9000 makes 45 widgets per hour, so in a day it makes 45 * 24 = 1080
  widgets. In a month it makes 1080 * 30 = 32400 widgets. It sells these for $6 each,
  so it earns $32400 * $6 = $<<32400*6=194400>>194400.

  The difference between the two is $259200 - $194400 = $<<259200-194400=64800>>64800.

  #### 64800


  '
- ' The Widgeteer 3000 makes 60 widgets an hour, so it makes 60 * 24 = <<60*24=1440>>1440
  widgets per day. It makes 1440 * 30 = <<1440*30=43200>>43200 widgets a month. The
  WidgetMaster 9000 makes 45 widgets an hour, so it makes 45 * 24 = <<45*24=1080>>1080
  widgets per day. It makes 1080 * 30 = <<1080*30=32400>>32400 widgets a month.

  The Widgeteer 3000 makes $6 * 43200 = $<<6*43200=259200>>259200 per month. The WidgetMaster
  9000 makes $6 * 32400 = $<<6*32400=194400>>194400 per month.

  So the Widgeteer 3000 makes $259200 - $194400 = <<259200-194400=64800>>$64800 more.

  #### 64800


  '
- ' First, we need to find how many widgets the two machines make in a day. The Widgeteer
  3000 makes 60 widgets per hour, and the WidgetMaster 9000 makes 45 widgets per hour.
  So the Widgeteer 3000 makes 60 * 24 = <<60*24=1440>>1440 widgets in a day, and the
  WidgetMaster 9000 makes 45 * 24 = <<45*24=1080>>1080 widgets in a day.

  Next, we need to find how many widgets the two machines make in a month. A month
  has 30 days, so the Widgeteer 3000 makes 1440 * 30 = <<1440*30=43200>>43200 widgets
  in a month, and the WidgetMaster 9000 makes 1080 * 30 = <<1080*30=32400>>32400 widgets
  in a month.

  Finally, we need to find how much more money Fern will make from the Widgeteer 3000
  vs. the WidgetMaster 9000. The Widgeteer 3000 makes 43200 widgets per month, and
  each widget sells for $6. So the Widgeteer 3000 makes $43200 * 6 = <<43200*6=259200>>$259200
  per month.

  The WidgetMaster 9000 makes 32400 widgets per month, and each widget sells for $6.
  So the WidgetMaster 9000 makes $32400 * 6 = <<32400*6=194400>>$194400 per month.

  Fern will make $259200 - $194400 = <<259200-194400=64800>>$64800 more from the Widgeteer
  3000 vs. the WidgetMaster 9000.

  #### 64800


  '
- ' Fern will make $60 * $6 = $<<60*6=360>>360 per hour with the Widgeteer 3000.

  And $45 * $6 = $<<45*6=270>>270 per hour with the WidgetMaster 9000.

  So Fern will make $360 - $270 = $<<360-270=90>>90 more per hour with the Widgeteer
  3000.

  Each month, Fern will make $90 * 24 = $<<90*24=2160>>2160 more per month with the
  Widgeteer 3000.

  Over a month, Fern will make $2160 * 30 = $<<2160*30=64800>>64800 more with the
  Widgeteer 3000.

  #### 64800


  '
- ' Each widget makes $6.

  Widgeteer 3000 makes 60 widgets per hour. In 24 hours, it will make 60 * 24 = <<60*24=1440>>1440
  widgets.

  WidgetMaster 9000 makes 45 widgets per hour. In 24 hours, it will make 45 * 24 =
  <<45*24=1080>>1080 widgets.

  The difference is 1440 - 1080 = <<1440-1080=360>>360 widgets.

  Each widget makes $6. So the Widgeteer 3000 will make 360 * 6 = <<360*6=2160>>2160.

  #### 2160


  '
- ' First we need to calculate how many widgets each will make in a month. The Widgeteer
  3000 will make 60 * 24 * 30 = <<60*24*30=43200>>43200 widgets.

  The WidgetMaster 9000 will make 45 * 24 * 30 = <<45*24*30=32400>>32400 widgets.

  So the Widgeteer 3000 will make $43200 * $6 = <<43200*6=259200>>$259200.

  The WidgetMaster 9000 will make $32400 * $6 = <<32400*6=194400>>$194400.

  So the Widgeteer 3000 will make $259200 - $194400 = <<259200-194400=64800>>$64800
  more than the WidgetMaster 9000.

  #### 64800


  '
