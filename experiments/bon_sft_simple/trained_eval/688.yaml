gt_answer: '<PERSON> has to spend 20+40+20 = <<20+40+20=80>>80 minutes on homework.

  <PERSON> has 3 hours*60 minutes= <<3*60=180>>180 minutes before dinner.

  Thus, <PERSON> has 180-80=<<180-80=100>>100 minutes for his nap.

  #### 100'
is_corrects:
- true
- true
- true
- true
- true
- false
- true
- true
- true
- true
prompt: 'Question: Grandma <PERSON> created statues of turtles for her front lawn.  The
  first year, she created 4 statues and placed them on her lawn.  The second year,
  she quadrupled the number of statues on her front lawn.  In the third year, she
  added another 12 statues to the front lawn, but a hail storm broke 3 of the statues,
  which she threw away.  In the fourth year, she added twice as many new statues as
  had been broken the year before.  At the end of the four years, how many turtle
  statues were on her front lawn?

  Answer: The second year, she quadrupled the number of statues in her front lawn,
  adding to the original 4 and bringing the total number to 4*4=<<4*4=16>>16 statues.

  In the third year, she added another 12 statues, bringing the total number to 16+12=28
  statues.

  But a hail storm broke 3, reducing the number to 28-3=<<28-3=25>>25 statues.

  In the fourth year, she added twice as many new statues as had been broken the year
  before, adding 2*3=<<2*3=6>>6 statues.

  Thus, the addition of these 6 statues brought the total number of statues to 25+6=<<25+6=31>>31
  statues.

  #### 31


  Question: If <PERSON>u has 9 books and <PERSON> has 4 times as many books as <PERSON>u, how many
  books do <PERSON>u and <PERSON> have in total?

  Answer: <PERSON> has 4 * 9 books belonging to <PERSON>u = <<4*9=36>>36 books.

  So the pair have a combined total of 36 books belonging to <PERSON> + 9 books belonging
  to <PERSON>u = <<36+9=45>>45 books.

  #### 45


  Question: <PERSON> is very tired. He decides to take a nap, but he wants to finish his
  homework first. If John has 20 minutes of math homework, 40 minutes of reading homework,
  and 20 minutes of history homework and 3 hours before he has to eat dinner, how
  many minutes does John have to nap?

  Answer:'
question: John is very tired. He decides to take a nap, but he wants to finish his
  homework first. If John has 20 minutes of math homework, 40 minutes of reading homework,
  and 20 minutes of history homework and 3 hours before he has to eat dinner, how
  many minutes does John have to nap?
samples:
- ' There are 3 hours = 3 * 60 = <<3*60=180>>180 minutes before dinner.

  John has a total of 20 + 40 + 20 = <<20+40+20=80>>80 minutes of homework.

  Therefore, John will have 180 - 80 minutes = <<180-80=100>>100 minutes left.

  #### 100


  '
- ' John has 20 minutes of math homework, 40 minutes of reading homework, and 20 minutes
  of history homework.

  So he has 20 + 40 + 20 minutes of homework in total = <<20+40+20=80>>80 minutes.

  John has 3 hours before he has to eat dinner. He has 3 hours * 60 minutes = <<3*60=180>>180
  minutes.

  So he has 180 - 80 minutes = <<180-80=100>>100 minutes left.

  #### 100


  '
- ' John has a total of 20 + 40 + 20 = <<20+40+20=80>>80 minutes of homework.

  3 hours is 3 * 60 = <<3*60=180>>180 minutes.

  Since John has 3 hours before he has to eat dinner, he has 180 minutes before dinner.

  In order to finish his homework, John has 180 - 80 = <<180-80=100>>100 minutes.

  #### 100


  '
- ' John has 3 hours = <<3*60=180>>180 minutes.

  He has 20 + 40 + 20 = 80 minutes of homework.

  So he has 180 - 80 = <<180-80=100>>100 minutes for his nap.

  #### 100


  '
- ' John has 20 minutes of math homework, 40 minutes of reading homework, and 20 minutes
  of history homework. These add up to 20+40+20=<<20+40+20=80>>80 minutes of homework.

  John has to finish his homework before he eats dinner. He has 3 hours before he
  eats dinner. 3 hours = 3 * 60 = <<3*60=180>>180 minutes.

  John has 180 minutes available before dinner. After completing his homework, he
  will have 180 - 80 minutes available for nap. 180 - 80 = <<180-80=100>>100.

  #### 100


  '
- ' John has 20 + 40 + 20 = <<20+40+20=80>>80 minutes of homework. So he has 3 hours
  = 3 * 60 = <<3*60=180>>180 minutes before he has to eat dinner.

  #### 180


  '
- ' John has a total of 20 + 40 + 20 = <<20+40+20=80>>80 minutes of homework.

  He has 3 hours = 180 minutes before he has to eat dinner.

  He will have a total of 180 - 80 = <<180-80=100>>100 minutes before he has to eat
  dinner.

  #### 100


  '
- ' The time John has to nap is the time he has before he has to eat dinner minus
  the time he needs for his homework.

  The time he has before he has to eat dinner is the number of minutes in 3 hours
  = <<3 hours * 60 minutes/hour=180>>180 minutes.

  The time he needs for his homework is 20 minutes for math + 40 minutes for reading
  + 20 minutes for history = <<20+40+20=80>>80 minutes.

  So the time he has to nap is the time he has before he has to eat dinner minus the
  time he needs for his homework = <<180-80=100>>100 minutes.

  #### 100


  '
- ' In total, John has 20+40+20=<<20+40+20=80>>80 minutes of homework.

  John has 3 hours, which is 3 * 60 = <<3*60=180>>180 minutes before he has to eat
  dinner.

  John has a total of 180 minutes before he has to eat dinner.

  He has 80 minutes of homework.

  So, the amount of time he has left to nap is 180-80=<<180-80=100>>100 minutes.

  #### 100


  '
- ' John has 20 minutes of math homework, 40 minutes of reading homework, and 20 minutes
  of history homework. He will spend 20+40+20 minutes = <<20+40+20=80>>80 minutes
  on homework.

  He has 3 hours before he has to eat dinner. There are 3 * 60 = <<3*60=180>>180 minutes
  in 3 hours.

  He can nap in 180 - 80 = <<180-80=100>>100 minutes.

  #### 100


  '
