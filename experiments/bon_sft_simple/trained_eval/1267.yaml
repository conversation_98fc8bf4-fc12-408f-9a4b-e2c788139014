gt_answer: '2-legged animals have 2 legs. Therefore 10 2-legged animals will have
  10*2=<<10*2=20>>20 legs

  4-legged animals have 4 legs. Therefore 15 4-legged animals will have 15*4=<<15*4=60>>60
  legs

  In total there are 20+60= <<20+60=80>>80 legs

  A pair of legs equals 2 legs. If there are 80 legs on the farm, there will be 80/2=
  <<80/2=40>>40 pairs of legs

  #### 40'
is_corrects:
- false
- false
- false
- false
- false
- false
- false
- false
- false
- false
prompt: "Question: <PERSON><PERSON> is planning the lunch menu for an elementary school. There\
  \ are 5 third grade classes with 30 students each, 4 fourth grade classes with 28\
  \ students each, and 4 fifth grade classes with 27 students each. Each student gets\
  \ a hamburger, which costs $2.10, some carrots, which cost $0.50, and a cookie,\
  \ which cost $0.20. How much does one lunch for all the students cost?\nAnswer:\
  \ First find the total number of students in third grade 5 classes * 30 students/class\
  \ = <<5*30=150>>150 students\nThen find the total number of students in fourth grade\
  \ 4 classes * 28 students/class = <<4*28=112>>112 students\nThen find the total\
  \ number of student in fifth grade 4 classes * 27 students/class = <<4*27=108>>108\
  \ students\nNow add the number of students in each grade to find the total number\
  \ of students 150 students + 112 students + 108 students = <<150+112+108=370>>370\
  \ students\nNow find the total cost of one lunch $2.10 + $0.50 + $0.20 = $<<2.10+0.50+0.20=2.80>>2.80\n\
  Now multiply that cost by the number of students to find the total cost for the\
  \ school $2.80/student * 370 students = $<<2.80*370=1036>>1036\n#### 1036\n\nQuestion:\
  \ Tricia is a third of Amilia\u2019s age and Amilia is a quarter of Yorick\u2019\
  s age. Yorick is twice Eugene\u2019s age and Khloe is a third of Eugene\u2019s age.\
  \ Rupert is 10 years older than Khloe but 2 years younger than Vincent who is 22\
  \ years old. How old, in years, is Tricia?\nAnswer: Rupert is younger than Vincent\
  \ by 2 years, so he is 22 years old \u2013 2 years = <<22-2=20>>20 years old.\n\
  Khloe is 10 years younger than Rupert so she is 20 years old \u2013 10 years = 10\
  \ years old.\nEugene is 3 times older than Khloe so he is 10 years old * 3 = <<10*3=30>>30\
  \ years old.\nYorick is twice Eugene\u2019s age so he is 30 years old * 2 = <<30*2=60>>60\
  \ years old.\nAmilia is a quarter of Yorick\u2019s age so she is 60 years old /\
  \ 4 = <<60/4=15>>15 years old.\nTricia is a third of Amilia\u2019s age so she is\
  \ 15 years old / 3 = <<15/3=5>>5 years old.\n#### 5\n\nQuestion: A farm has 10 2-legged\
  \ animals and 15 4-legged animals. If all animals are healthy, how many pairs of\
  \ animal legs in total are on the farm?\nAnswer:"
question: A farm has 10 2-legged animals and 15 4-legged animals. If all animals are
  healthy, how many pairs of animal legs in total are on the farm?
samples:
- ' The farm has 10 2-legged animals, which are 10 * 2 = <<10*2=20>>20 legs.

  The farm has 15 4-legged animals, which are 15 * 4 = <<15*4=60>>60 legs.

  In total there are 20 legs + 60 legs = <<20+60=80>>80 legs.

  #### 80


  '
- ' There are 10 2-legged animals and 15 4-legged animals. The number of 2-legged
  animals is 10 and the number of 4-legged animals is 15.

  The total number of legs is (10 * 2) + (15 * 4) = 20 + 60 = 80

  #### 80


  '
- ' 10 2-legged animals = <<10*2=20>>20 legs

  15 4-legged animals = <<15*4=60>>60 legs

  In total there are 20 legs + 60 legs = <<20+60=80>>80 legs

  #### 80


  '
- ' There are 10 2-legged animals, so there are 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals, so there are 15 * 4 = <<15*4=60>>60 legs.

  So there are 20 + 60 = <<20+60=80>>80 legs in total.

  #### 80


  '
- ' The total number of legs on the farm is (10 x 2) + (15 x 4) = <<10*2+15*4=20+60=80>>80.

  There are 80 legs in total.

  #### 80


  '
- ' There are 10 2-legged animals so there are 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals so there are 15 * 4 = <<15*4=60>>60 legs.

  There are 20 + 60 = <<20+60=80>>80 legs.

  #### 80


  '
- ' There are 10 2-legged animals so there are 10 * 2 = <<10*2=20>>20 legs for the
  2-legged animals.

  There are 15 4-legged animals so there are 15 * 4 = <<15*4=60>>60 legs for the 4-legged
  animals.

  There are 20 legs + 60 legs = <<20+60=80>>80 legs on the farm.

  #### 80


  '
- ' There are 10 2-legged animals, so they have 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals, so they have 15 * 4 = <<15*4=60>>60 legs.

  There are 20 + 60 = <<20+60=80>>80 legs in total.

  #### 80


  '
- ' There are 10 2-legged animals, so that is 10 * 2 = <<10*2=20>>20 legs.

  There are 15 4-legged animals, so that is 15 * 4 = <<15*4=60>>60 legs.

  Add the legs together to find the total 20 legs + 60 legs = <<20+60=80>>80 legs

  #### 80


  '
- ' We know there are 10 2-legged animals and 15 4-legged animals. 2-legged animals
  have 2 legs, so there are 2*10 = <<2*10=20>>20 legs. 4-legged animals have 4 legs,
  so there are 4*15 = <<4*15=60>>60 legs. Total legs are 20 legs + 60 legs = <<20+60=80>>80
  legs.

  #### 80


  '
