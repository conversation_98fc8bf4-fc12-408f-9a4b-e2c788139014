gt_answer: 'The other two pizzas cost 64-30 = <<64-30=34>>34 dollars.

  Each of the other two pizzas cost 34/2 = <<34/2=17>>17 dollars each.

  #### 17'
is_corrects:
- true
- true
- false
- false
- true
- false
- true
- true
- true
- false
prompt: 'Question: <PERSON> read 23 books, <PERSON> read 12 books and <PERSON><PERSON><PERSON> read 17 books.
  <PERSON> and <PERSON> read 3 of the same books and all three had read the same book as well.
  In total how many different books have <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> read?

  Answer: <PERSON> and <PERSON> together read 23 + 12 = <<23+12=35>>35 books

  3 of these books were duplicates so <PERSON> and <PERSON> together read 35 - 3 = <<35-3=32>>32
  unique books

  All three read 32 + 17 = <<32+17=49>>49 books

  Because the books are being counted three times, the book is counted 3 - 1 = <<3-1=2>>2
  extra times.

  All together they have read 49 - 2 = <<49-2=47>>47 different books

  #### 47


  Question: <PERSON> has a chicken farm with 4 chickens. His chickens lay 3 eggs each
  day. If <PERSON> collects all the eggs after 3 days, then how many eggs will <PERSON> have
  collected?

  Answer: Each chicken lays 3 eggs per day and there are 4 chickens 3 * 4 = <<3*4=12>>12

  He will collect all the eggs after 3 days 12 * 3 = <<12*3=36>>36

  #### 36


  Question: Four friends ordered four pizzas for a total of 64 dollars. If two of
  the pizzas cost 30 dollars, how much did each of the other two pizzas cost if they
  cost the same amount?

  Answer:'
question: Four friends ordered four pizzas for a total of 64 dollars. If two of the
  pizzas cost 30 dollars, how much did each of the other two pizzas cost if they cost
  the same amount?
samples:
- ' First, find out how much the other pizzas cost. We can do this by subtracting
  30 from the total cost

  64 - 30 = <<64-30=34>>34

  Then, divide the cost of the other pizzas by 2 since they are the same

  34/2 = <<34/2=17>>17

  #### 17


  '
- ' 64 - 30 = <<64-30=34>>34

  They have 34 dollars for the other two pizzas

  They are the same price so 34/2 = <<34/2=17>>17

  #### 17


  '
- ' The total cost is 64

  Two pizzas cost 30 dollars

  The total cost of the other pizzas is 64 - 30 = <<64-30=34>>34

  #### 34


  '
- ' They paid 64 dollars for 4 pizzas

  Two of the pizzas cost 30 dollars

  This leaves 64 - 30 = <<64-30=34>>34 for the other 2 pizzas

  #### 34


  '
- ' If two pizzas cost 30 dollars, then the other two pizzas cost 64 - 30 = <<64-30=34>>34

  The two pizzas cost 34 dollars in total, so each pizza cost 34 / 2 = <<34/2=17>>17

  #### 17


  '
- ' We can find the cost of each of the other pizzas by subtracting the cost of the
  two pizzas that cost 30 dollars from the total cost of the pizzas

  30 * 2 = <<30*2=60>>60

  The total cost of the pizzas is 64 dollars, so the cost of the other two pizzas
  is 64 - 60 = <<64-60=4>>4

  #### 4


  '
- ' The total cost of the pizzas is 64

  Two pizzas cost 30

  The cost of the other two pizzas is 64 - 30 = <<64-30=34>>34

  The cost of each pizza is 34 / 2 = <<34/2=17>>17

  #### 17


  '
- ' The total cost of the pizzas was 64 dollars

  2 of them cost 30 dollars 64 - 30 = <<64-30=34>>34

  So the remaining pizzas cost 34 dollars

  They are the same cost 34 / 2 = <<34/2=17>>17

  #### 17


  '
- ' The pizzas cost a total of 64 dollars. Two pizzas cost 30 dollars. So the other
  two pizzas cost 64 - 30 = <<64-30=34>>34 dollars

  Because the pizzas are the same, they cost 34 / 2 = <<34/2=17>>17 dollars

  #### 17


  '
- ' Two pizzas cost 30 dollars 30 = <<30=30>>30

  The remaining pizzas cost 64 - 30 = <<64-30=34>>34

  #### 34


  '
