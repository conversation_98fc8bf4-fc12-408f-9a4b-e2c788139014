gt_answer: 'First find how many gigabytes are in 40% of the file: 200 GB * 40% = <<200*40*.01=80>>80
  GB

  Then divide that number by the download rate to find the time until Windows restarts:
  80 GB / 2 GB/minute = <<80/2=40>>40 minutes

  Then find the time to download the whole file after the restart: 200 GB / 2 GB/minute
  = <<200/2=100>>100 minutes

  Then add the time to download 40% of the file, to download the whole file, and to
  wait for Windows to update: 40 minutes + 100 minutes + 20 minutes = <<40+100+20=160>>160
  minutes

  #### 160'
is_corrects:
- false
- false
- false
- false
- false
- false
- false
- false
- false
- false
prompt: 'Question: There are 235 books in a library. On Tuesday, 227 books are taken
  out. On Thursday, 56 books are brought back and 35 books are taken out again on
  Friday. How many books are there now?

  Answer: On Tuesday, 227 books were taken, so there are 235 - 227= <<235-227=8>>8
  books left.

  56 books were brought back on Thursday, so there are 8 + 56 = <<8+56=64>>64 books
  in the library.

  Finally, 35 books are taken out on Friday, leaving 64 - 35 = <<29=29>>29 books available.

  #### 29


  Question: <PERSON><PERSON> makes $21 of profit every time she sells 3 shirts and four times
  as much profit when she sells two pairs of sandals. How much profit will she make
  if she sells 7 shirts and 3 pairs of sandals?

  Answer: <PERSON><PERSON> makes $21 * 4 = $<<21*4=84>>84 of profit for two pairs of sandals.

  <PERSON><PERSON> makes $84 / 2 pairs = $<<84/2=42>>42 of profit per pair of shoes.

  <PERSON>ony makes $21 / 3 shirts = $<<21/3=7>>7 of profit per shirt.

  For seven shirts, Melony will make 7 shirts * $7 = $<<7*7=49>>49 of profit.

  For three pairs of sandals, Melony will make 3 pairs * $42/pair = $<<3*42=126>>126
  of profit.

  Melony will make a total of $49 + $126 = $<<49+126=175>>175 of profit.

  #### 175


  Question: Carla is downloading a 200 GB file. Normally she can download 2 GB/minute,
  but 40% of the way through the download, Windows forces a restart to install updates,
  which takes 20 minutes. Then Carla has to restart the download from the beginning.
  How load does it take to download the file?

  Answer:'
question: Carla is downloading a 200 GB file. Normally she can download 2 GB/minute,
  but 40% of the way through the download, Windows forces a restart to install updates,
  which takes 20 minutes. Then Carla has to restart the download from the beginning.
  How load does it take to download the file?
samples:
- ' Carla downloaded 2 GB/minute for 40% of the file, or 2 * 40/100 * 200 = <<2*40/100*200=160>>160
  GB. The download took 160/2 = <<160/2=80>>80 minutes.

  After the restart, Carla downloaded the remaining 200 - 160 = <<200-160=40>>40 GB.
  The download took 20 minutes, so Carla downloaded 40/20 = <<40/20=2>>2 GB/minute.

  The total download time is 80 + 20 = <<80+20=100>>100 minutes. Carla downloaded
  2 GB/minute for 20 minutes, or 2 * 20 = <<2*20=40>>40 GB. Then she downloaded 160
  GB, for a total of 40 + 160 = <<40+160=200>>200 GB. The total download time is 100
  minutes.

  #### 100


  '
- ' Carla is downloading 2 GB/minute. After 40% of the file is downloaded, Carla has
  downloaded 40% * 200 GB = <<40%*200=80>>80 GB.

  She is left with 200 - 80 = <<200-80=120>>120 GB.

  The remaining download is 120 GB / 2 GB/minute = <<120/2=60>>60 minutes long.

  Since the download takes 60 minutes after Windows forces the restart, Carla has
  to wait 20 minutes for the restart, then 60 minutes to finish the download. The
  total time is <<20+60=80>>80 minutes.

  #### 80


  '
- ' Carla downloads 2 GB/minute. The file is 200 GB, so it will take 200 GB/2 GB/min
  = <<200/2=100>>100 minutes to download the file.

  But when Windows restarts the download, Carla downloads from the beginning. So she
  has to download 40% of the file again. 40% of the file is 200 GB * 40/100 = <<200*40/100=80>>80
  GB.

  It will take Carla 80 GB/2 GB/min = <<80/2=40>>40 minutes to download 80 GB.

  So in total, it will take Carla 100 minutes + 40 minutes = <<100+40=140>>140 minutes
  to download the file.

  #### 140


  '
- ' Carla is downloading 200 GB, but then she has to restart the download. So she
  has to download the file twice.

  Carla can download 2 GB/minute. So she can download 2 GB/minute * 60 minutes = <<2*60=120>>120
  GB in an hour.

  Carla has to download 200 GB twice. So it will take Carla 200 GB/120 GB per hour
  = <<200/120=1.67>>1.67 hours to download the file.

  #### 1.67


  '
- ' 40% of 200 GB = 0.4 * 200 = <<0.4*200=80>>80 GB.

  80 GB is 40% of 200 GB, so Carla has downloaded 80 GB when Windows forces the restart.

  When Windows forces the restart, Carla has downloaded 80 GB in 80 GB / 2 GB/minute
  = <<80/2=40>>40 minutes.

  After the restart, Carla has to download 200 GB - 80 GB = <<200-80=120>>120 GB.

  120 GB / 2 GB/minute = <<120/2=60>>60 minutes.

  In total, Carla has to download the file in 40 minutes + 60 minutes = <<40+60=100>>100
  minutes.

  #### 100


  '
- ' Carla downloads 40% of the file, which is 0.4 * 200 = <<0.4*200=80>>80 GB.

  At a rate of 2 GB/minute, Carla can download 80 GB in 80/2 = <<80/2=40>>40 minutes.

  After the restart, Carla has to download 200 - 80 = <<200-80=120>>120 GB.

  Carla downloads 120 GB at a rate of 2 GB/minute, which takes 120/2 = <<120/2=60>>60
  minutes.

  The total download time is 40 + 20 + 60 = <<40+20+60=120>>120 minutes.

  #### 120


  '
- ' Carla is downloading 200 GB, but the download is interrupted after 40% of the
  file is downloaded, or 0.40 * 200 = <<0.40*200=80>>80 GB.

  The remaining 200 - 80 = <<200-80=120>>120 GB must be downloaded.

  Carla downloads 2 GB per minute, or 120 / 2 = <<120/2=60>>60 minutes.

  Adding the initial 20 minutes it took to install updates, the total time it takes
  to download the file is 60 + 20 = <<60+20=80>>80 minutes.

  #### 80


  '
- ' Carla can download 2 GB/minute. For 40% of the way, Carla downloads 2 * 0.4 *
  60 = <<2*0.4*60=48>>48 GB.

  The file is 200 GB, so Carla has downloaded 48 GB. Carla has to restart from the
  beginning.

  The time left to download the file is 200 - 48 = <<200-48=152>>152 GB.

  The time Carla will take to download the file is 152/2 = <<152/2=76>>76 minutes.

  #### 76


  '
- ' Carla has to download 200 GB. 40% of 200 GB is 40% * 200 = <<40*200=80>>80 GB.

  Carla downloads 2 GB per minute. So Carla will download 80 GB in 80/2 = <<80/2=40>>40
  minutes.

  After the restart, Carla has to download the remaining 200 - 80 = <<200-80=120>>120
  GB.

  Carla downloads 2 GB per minute, so she will download 120 GB in 120/2 = <<120/2=60>>60
  minutes.

  In total, it will take Carla 40 + 60 = <<40+60=100>>100 minutes to download the
  file.

  #### 100


  '
- ' Carla downloads at a rate of 2 GB/minute.

  She downloads for 40% of the time and then the download stops.

  She downloads for 40% * 200 GB = <<40*200=8000>>8000 GB.

  She downloads at 2 GB/minute, so she downloads for 8000 GB / 2 GB/minute = <<8000/2=4000>>4000
  minutes.

  She downloads for 4000 minutes and then restarts the download.

  She downloads for 4000 minutes, and then she downloads for 20 minutes.

  She downloads for 4000 + 20 = <<4000+20=4020>>4020 minutes.

  #### 4020


  '
