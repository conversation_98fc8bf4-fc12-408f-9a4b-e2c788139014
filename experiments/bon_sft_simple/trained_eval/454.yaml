gt_answer: 'In one day, <PERSON> and <PERSON> eat 4 + 1 = <<4+1=5>>5 apples.

  In 30 days, they eat 30 * 5 = <<30*5=150>>150 apples.

  #### 150'
is_corrects:
- false
- false
- false
- false
- false
- false
- false
- false
- false
- false
prompt: "Question: <PERSON> and his brother <PERSON> bought presents for their father.\
  \ <PERSON> bought a wallet at $50 and two pairs of sneakers at $100 each pair, while\
  \ <PERSON> bought a backpack at $100 and two pairs of jeans at $50 each pair. How\
  \ much did they spend in all?\nAnswer: Two pairs of sneakers cost 2 x $100 = $<<2*100=200>>200.\n\
  So, <PERSON> spent $50 + $200 = $<<50+200=250>>250.\nTwo pairs of jeans cost 2 x\
  \ $50 = $<<2*50=100>>100.\nThus, <PERSON> spent $100 + $100 = $<<100+100=200>>200.\n\
  Therefore, they spent a total of $250 + $200 = $<<250+200=450>>450 in all.\n####\
  \ 450\n\nQuestion: <PERSON> bought 7 packs of red bouncy balls and 6 packs of yellow\
  \ bouncy balls. Each pack contained 18 bouncy balls. How many more red bouncy balls\
  \ than yellow bouncy balls did <PERSON> buy?\nAnswer: <PERSON> bought 7 packs \xD7 18 balls/pack\
  \ = <<7*18=126>>126 red bouncy balls.\nShe also bought 6 packs \xD7 18 balls/pack\
  \ = <<6*18=108>>108 yellow bouncy balls.\nKate bought 126 balls - 108 balls= <<126-108=18>>18\
  \ more red bouncy balls than yellow bouncy balls.\n#### 18\n\nQuestion: Marin and\
  \ his neighbor Nancy each eat 4 apples a day. How many apples do they eat in 30\
  \ days?\nAnswer:"
question: Marin and his neighbor Nancy each eat 4 apples a day. How many apples do
  they eat in 30 days?
samples:
- ' Marin eats 4 apples/day, so in 30 days he eats 4 x 30 = <<4*30=120>>120 apples.

  Nancy also eats 4 apples/day, so in 30 days she eats 4 x 30 = <<4*30=120>>120 apples.

  In total, they eat 120 apples + 120 apples = <<120+120=240>>240 apples.

  #### 240


  '
- " Marin eats 4 apples per day \xD7 30 days = <<4*30=120>>120 apples.\nNancy also\
  \ eats 4 apples per day \xD7 30 days = <<4*30=120>>120 apples.\nTogether, they eat\
  \ 120 apples + 120 apples = <<120+120=240>>240 apples.\n#### 240\n\n"
- " Marin eats 4 apples a day.\nSo, in 30 days, he eats 4 apples/day \xD7 30 days=\
  \ <<4*30=120>>120 apples.\nNancy eats 4 apples a day.\nSo, in 30 days, she eats\
  \ 4 apples/day \xD7 30 days= <<4*30=120>>120 apples.\nMarin and Nancy eat 120 apples\
  \ + 120 apples= <<120+120=240>>240 apples in 30 days.\n#### 240\n\n"
- " They eat 4 apples a day.\nSo, in 30 days, they eat 4 apples/day \xD7 30 days =\
  \ <<4*30=120>>120 apples.\n#### 120\n\n"
- ' Marin eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  Nancy eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  Marin and Nancy eat 120 apples + 120 apples = <<120+120=240>>240 apples in total.

  #### 240


  '
- ' Marin eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  Nancy eats 4 apples/day * 30 days = <<4*30=120>>120 apples.

  In total, they eat 120 apples + 120 apples = <<120+120=240>>240 apples.

  #### 240


  '
- " Marin eats 4 apples \xD7 30 days = <<4*30=120>>120 apples.\nNancy eats 4 apples\
  \ \xD7 30 days = <<4*30=120>>120 apples.\nSo, Marin and Nancy together eat 120 apples\
  \ + 120 apples = <<120+120=240>>240 apples.\n#### 240\n\n"
- " Marin eats 4 apples/day \xD7 30 days = <<4*30=120>>120 apples in 30 days.\nNancy\
  \ eats 4 apples/day \xD7 30 days = <<4*30=120>>120 apples in 30 days.\nTherefore,\
  \ Marin and Nancy eat a total of 120 apples + 120 apples = <<120+120=240>>240 apples\
  \ in 30 days.\n#### 240\n\n"
- " Marin eats 4 apples/day \xD7 30 days = <<4*30=120>>120 apples in 30 days.\nNancy\
  \ eats 4 apples/day \xD7 30 days = <<4*30=120>>120 apples in 30 days.\nMarin and\
  \ Nancy eat 120 apples + 120 apples = <<120+120=240>>240 apples in 30 days.\n####\
  \ 240\n\n"
- " Marin eats 4 apples \xD7 30 days = <<4*30=120>>120 apples.\nNancy eats 4 apples\
  \ \xD7 30 days = <<4*30=120>>120 apples.\nSo, they eat 120 + 120 = <<120+120=240>>240\
  \ apples.\n#### 240\n\n"
