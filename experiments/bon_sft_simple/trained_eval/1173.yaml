gt_answer: 'If the regular box has 100 pods and the special one has 20% more, then
  the new box has 100 * 20% = 20 more pods

  If the new one has 20 more pods than the regular one, the new box has a total of
  20+100 = <<20+100=120>>120 pods

  If the cost of the regular box ($12) is the same as the new box, then the new box
  also costs $12

  If the new box has 120 pods and the price for the box is $12, then each pod costs
  $12/120 = $<<12/120=0.10>>0.10

  If 1 pod costs $0.10 and is used in 1 cycle, then each cycle costs = $<<1*0.10=0.10>>0.10

  If 1 cycle costs $0.10, then for $1 I could run $1/$0.1 = 10 cycles

  #### 10'
is_corrects:
- true
- true
- false
- true
- false
- false
- true
- true
- false
- false
prompt: 'Question: Bob enters cycling competitions every single week and hopes to
  win the 100 dollar grand prize each time. For the first 2 weeks, he managed first
  place and got 100 dollars each week. He is saving up for a puppy that costs 1000
  dollars. What is the minimum number of additional weeks Bob must win first place?

  Answer: Two weeks already passed and <PERSON> has 200 dollars.

  Bob needs 1000-200=<<1000-200=800>>800 more dollars.

  Bob needs to win the competition for at least 800/100=<<800/100=8>>8 more weeks.

  #### 8


  Question: For the funfair, the school organizers ordered 30 rolls of tickets. Each
  roll of tickets has 100 tickets. The 4th graders bought 30% of the tickets while
  the 5th graders bought 50% of the remaining tickets. Then the 6th graders bought
  a total of 100 tickets. How many tickets were left unsold?

  Answer: There were a total of 30 x 100 = <<30*100=3000>>3000 tickets ordered by
  the school organizers.

  The 4th graders bought 3000 x 30/100 = <<3000*30/100=900>>900 tickets.

  So, there were 3000 - 900 = <<3000-900=2100>>2100 tickets left for the other two
  grade levels.

  The 5th graders bought 2100 x 50/100 = <<2100*50/100=1050>>1050 tickets.

  So, 2100 - 1050 = <<2100-1050=1050>>1050 tickets were left then.

  Since 100 tickets were bought by the 6th graders, therefore 1050 - 100 = <<950=950>>950
  tickets were left unsold.

  #### 950


  Question: A regular box of 100 dishwasher pods costs $12. There''s currently a special
  size box for the same price that has 20% more pods. How many dishwashing cycles
  with the new box can you run for $1 if you use 1 pod per cycle?

  Answer:'
question: A regular box of 100 dishwasher pods costs $12. There's currently a special
  size box for the same price that has 20% more pods. How many dishwashing cycles
  with the new box can you run for $1 if you use 1 pod per cycle?
samples:
- ' The regular box of 100 pods costs $12. The new box with 20% more pods is 100 +
  20 = <<100+20=120>>120 pods.

  The price of the new box is also $12, so it''s cheaper per pod. In terms of cents,
  that''s $12/120 = <<12/120=0.1>>0.1 cents per pod.

  For $1, you can run 0.1/0.01 = <<0.1/0.01=10>>10 cycles.

  #### 10


  '
- ' The special box contains 20% more pods than the regular box. So, it contains 100
  x 20/100 + 100 = <<100*20/100+100=120>>120 pods. The special box costs $12. So,
  the price per pod is $12/120 = <<12/120=1/10>>1/10 dollars. So, for $1, you can
  run 1/0.1 = <<1/0.1=10>>10 cycles.

  #### 10


  '
- ' The new box has 20% more dishwasher pods. 20% of 100 is 20/100*100 = <<20/100*100=20>>20.

  So, it has 100 + 20 = <<100+20=120>>120 dishwasher pods.

  The total cost of the new box is still $12.

  So, for $12, you can run 12/1 = <<12/1=12>>12 dishwasher cycles.

  #### 12


  '
- ' Let''s first figure out how many pods are in the new box. Since there are 20%
  more pods, there are 100 x 120/100 = <<100*120/100=120>>120 pods in the new box.

  Now let''s figure out how many dishwashing cycles you can run for $1 with the new
  box. You can run 120/12 = <<120/12=10>>10 dishwashing cycles with $1.

  #### 10


  '
- ' The new box costs $12, just like the regular box. So it must have more pods.

  The new box has 100 x 1.2 = <<100*1.2=120>>120 pods.

  For $1, you can buy 12/1 = <<12/1=12>>12 pods.

  #### 12


  '
- ' There are 100 + 20% of 100 = <<100+20%*100=100+20/100*100=100+20/100*100=100+20/100*100=120>>120
  dishwasher pods in the new box.

  One dollar can be used to buy $\frac{\12}{120}$ = <<$\frac{12}{120}=\frac{1}{10}$>>
  cycles of dishwasher pods.

  #### $\frac{1}{10}$


  '
- ' If you use 1 pod per cycle, then you can run 100 cycles with the regular box for
  $12, which is $0.12 per cycle.

  The new box has 20% more pods than the regular box. This means the new box has 100
  + 100 * 20/100 = <<100+100*20/100=100+20=120>>120 pods.

  You can run 120 cycles with the new box for $12, which is $0.10 per cycle.

  #### 10


  '
- ' The new box has 20% more pods than the regular box. If the regular box has 100
  pods, then the new box has 100 + 100 x 20/100 = <<100+100*20/100=100+20=120>>120
  pods.

  If the new box costs $12, then each pod costs $12 / 120 = <<12/120=1/10=0.1>>0.1
  dollars.

  So, for $1, you can run 1 / 0.1 = <<1/0.1=10>>10 cycles.

  #### 10


  '
- ' The new box has 20% more pods than the regular box. The new box has 100 + 20%
  of 100 = <<100+20%of100=100+0.20*100=100+20=120>>120 pods.

  The cost of the new box is $12. The cost per pod of the new box is $12/120 = <<12/120=0.10>>0.10.

  The cost per cycle for the new box is $0.10/1 = <<0.10/1=0.10>>0.10.

  #### 0.10


  '
- ' The new box has 20% more pods than the regular box. So the new box has 1.2 x 100
  = <<1.2*100=120>>120 pods.

  The price of both boxes are the same. So the price per pod is $12 / 120 = <<12/120=1/10=0.1>>0.1.

  Therefore, you can run 0.1 / 1 = <<0.1/1=0.1>>0.1 cycles for $1.

  #### 0.1


  '
