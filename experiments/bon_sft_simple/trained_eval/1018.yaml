gt_answer: "<PERSON> would take 18 \xF7 3 = <<18/3=6>>6 times longer if he read all 18\
  \ pages.\nThis means <PERSON> would spend 6 \xD7 10 = <<6*10=60>>60 minutes reading.\n\
  #### 60"
is_corrects:
- true
- true
- true
- true
- true
- true
- true
- true
- true
- true
prompt: "Question: <PERSON><PERSON><PERSON> makes bracelets to sell at the farmer\u2019s market every\
  \ weekend. Each bracelet takes twice as many small beads as it does large beads.\
  \ If each bracelet uses 12 large beads, and <PERSON><PERSON><PERSON> has 528 beads with equal amounts\
  \ of large and small beads, how many bracelets can she make for this weekend?\n\
  Answer: <PERSON><PERSON><PERSON> has 528/2 = <<528/2=264>>264 small beads.\nEach bracelet needs 12\
  \ * 2 = <<12*2=24>>24 small beads.\nThus, <PERSON><PERSON><PERSON> can make 264/24 = <<264/24=11>>11\
  \ bracelets for the farmer\u2019s market this weekend.\n#### 11\n\nQuestion: Four\
  \ cats live in the old grey house at the end of the road.  Their names are <PERSON><PERSON><PERSON>,\
  \ <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.  <PERSON><PERSON><PERSON> has three times more whiskers than <PERSON><PERSON>,\
  \ but half as many as <PERSON><PERSON><PERSON>.  <PERSON> has the same number of whiskers as the average\
  \ number of whiskers on the three other cats.  If <PERSON><PERSON> has 12 whiskers. how many\
  \ whiskers does <PERSON> have?\nAnswer: If Juniper has 12 whiskers, then <PERSON>uffy has\
  \ 3*12=<<12*3=36>>36 whiskers.\nSince Puffy has half as many whiskers and Scruffy,\
  \ then Scruffy has 36*2=<<36*2=72>>72 whiskers.\nSince the number of whiskers on\
  \ <PERSON> is the average between 12, 36, and 72 whiskers, then he has (12+36+72)/3=40\
  \ whiskers.\n#### 40\n\nQuestion: It takes James 10 minutes to read 3 pages of his\
  \ book before he goes to bed. He reads 18 pages of his book and then decides to\
  \ go to sleep. How long does James spend reading, in minutes?\nAnswer:"
question: It takes James 10 minutes to read 3 pages of his book before he goes to
  bed. He reads 18 pages of his book and then decides to go to sleep. How long does
  James spend reading, in minutes?
samples:
- ' James reads 18 pages of his book. He reads 3 pages in 10 minutes. To find the
  number of minutes James spends reading, we need to find the number of groups of
  3 pages he reads. To do this, we divide 18/3=<<18/3=6>>6.

  Thus, James reads 6 groups of 3 pages. He reads each group of 3 pages in 10 minutes.
  Therefore, he reads 6*10=<<6*10=60>>60 minutes.

  #### 60


  '
- ' James reads 18 pages.

  It takes him 10 minutes to read 3 pages, so it takes him 10/3 minutes to read 1
  page.

  Then it takes him 10/3 *18=60 minutes to read 18 pages.

  #### 60


  '
- ' If James reads 18 pages in total, then he reads 18/3=<<18/3=6>>6 sets of 3 pages.

  So he reads for 6*10=<<6*10=60>>60 minutes.

  #### 60


  '
- ' Since James reads 3 pages in 10 minutes, then he reads 1 page in 10/3 minutes.
  18 pages in 10/3 minutes will be 18*(10/3) minutes, or 60 minutes.

  #### 60


  '
- ' If he reads 3 pages in 10 minutes, then he reads 1 page in 10/3 minutes.

  In 18 pages, he spends 18*(10/3)=<<18*(10/3)=60>>60 minutes.

  #### 60


  '
- ' James reads 18 pages of his book.

  It takes him 10 minutes to read 3 pages of his book.

  Therefore, he reads 18/3=<<18/3=6>>6 times as much as 3 pages of his book.

  This means it takes him 6*10=<<6*10=60>>60 minutes to read his book.

  #### 60


  '
- ' James reads 18 pages. If it takes James 10 minutes to read 3 pages, then he reads
  1 page in 10/3 minutes.

  Therefore, James reads 18 pages in (10/3)*18=<<(10/3)*18=60>>60 minutes.

  #### 60


  '
- ' James reads 18/3=<<18/3=6>>6 sets of 3 pages.

  Each set takes him 10 minutes, so it takes him 6 * 10 = <<6*10=60>>60 minutes.

  #### 60


  '
- ' James reads 18/3=6*3=<<18/3=6*3=6>>6 sets of 3 pages.

  Each set takes 10 minutes. So James spent 6*10 minutes reading, which is 60 minutes.

  #### 60


  '
- ' James reads 18 pages.

  James reads 3 pages in 10 minutes, so he reads 1 page in 10/3 minutes.

  James reads 18 pages in 10/3*18 minutes.

  #### 60


  '
