gt_answer: 'The total number of posts from all members in a day is 1000 members *
  3 posts/member = <<1000*3=3000>>3000 posts.

  March has 31 days, so the total number of posts will be 31 days * 3000 posts/day
  = <<31*3000=93000>>93000 posts

  #### 93000'
prompt: 'Question: In a class of 25 students, students were asked if they like French
  fries, burgers, or neither. 15 students said they like French fries and 10 said
  they like burgers, with these statistics including the responses of 6 students who
  said they like both French fries and burgers. How many students do not like either
  food?

  Answer: There are 15 - 6 = <<15-6=9>>9 students who like French fries only.

  There are 10 - 6 = <<10-6=4>>4 students who like burgers only.

  So, 9 + 4 + 6 = <<9+4+6=19>>19 students like either French fries, burgers, or both.

  Hence, 25 - 19 = <<25-19=6>>6 students do not like either fries or burgers.

  #### 6


  Question: <PERSON> has 4 pens in the first week of a month. Every week her pen collection
  doubles. How many more pens will <PERSON> have than <PERSON> if <PERSON> will have 16 pens after
  a month?

  Answer: If <PERSON> has 4 pens in the first week of a month, she will have 4*2 = <<4*2=8>>8
  pens in the second week.

  After her pen collection doubles in the third week, <PERSON> will have 2*8 = <<2*8=16>>16
  pens.

  In the last week of the month, <PERSON> will have 2*16 = <<2*16=32>>32 pens after her
  pen collection.

  If Jane has 16 pens after a month, Alex will have 32-16 = <<32-16=16>>16 more pens
  than Jane.

  #### 16


  Question: A Reddit group has 1000 members. If each member posts an average of 3
  posts per day, what''s the total number of posts that the group will have in March?

  Answer:'
question: A Reddit group has 1000 members. If each member posts an average of 3 posts
  per day, what's the total number of posts that the group will have in March?
samples:
- ' There are 31 days in March. Each member posts 3 posts per day, so each member
  will post 31*3 = <<31*3=93>>93 posts in March.

  Since there are 1000 members, the total number of posts will be 1000*93 = <<1000*93=93000>>93,000
  posts in March.

  #### 93,000


  '
- ' March has 31 days. In 31 days, the group will have 3*31*1000 = <<3*31*1000=93000>>93,000
  posts.

  #### 93,000


  '
- ' There are 31 days in March. If each member posts 3 posts per day, the total number
  of posts per day is 3 x 1000 = <<3*1000=3000>>3000.

  In March, the total number of posts will be 31 x 3000 = <<31*3000=93000>>93,000.

  #### 93,000


  '
- ' There are 31 days in March.

  Each member will post 3 posts per day. So, the total number of posts per day is
  1000*3 = <<1000*3=3000>>3000 posts.

  In March, the total number of posts will be 3000*31 = <<3000*31=93000>>93,000 posts.

  #### 93000


  '
- ' There are 31 days in March.

  In a day, 1000 members will post 3*1000 = <<3*1000=3000>>3000 posts.

  In March, the group will have 31*3000 = <<31*3000=93000>>93,000 posts.

  #### 93000


  '
- ' There are approximately 31 days in March.

  So, the total number of posts will be 31 * 1000 * 3 = <<31*1000*3=93000>>93,000
  posts.

  #### 93,000


  '
- ' There are 31 days in March. 1000 members will post a total of 1000*3 = <<1000*3=3000>>3000
  posts per day.

  In March, the group will have a total of 31*3000 = <<31*3000=93000>>93,000 posts.

  #### 93000


  '
- ' In a month of 31 days, the group will have 31 days * 3 posts per day = <<31*3=93>>93
  posts per member.

  Since there are 1000 members, the total number of posts will be 93 posts per member
  * 1000 members = <<93*1000=93000>>93,000 posts.

  #### 93,000


  '
- ' There are 31 days in March.

  So, the total number of posts will be 1000*3*31 = <<1000*3*31=93,000>>93,000.

  #### 93,000


  '
- ' March has 31 days.

  Each member posts 3 posts per day. So each member will post 31*3 = <<31*3=93>>93
  posts in March.

  There are 1000 members. So the total number of posts in March is 1000*93 = <<1000*93=93000>>93,000.

  #### 93000


  '
