gt_answer: 'Together they found 214 + 432 + 86 = <<214+432+86=732>>732 seashells before
  cleaning.

  After cleaning, their final seashell is 732 - 67 = <<732-67=665>>665.

  #### 665'
prompt: 'Question: The sum of the ages of <PERSON>, <PERSON> and <PERSON> in three years
  is 150. Currently, <PERSON> is 4 years older than <PERSON>. If <PERSON>''s age is 40,
  calculate <PERSON>''s age three years from now?

  Answer: The age of <PERSON> three years from now will be 40+3 = <<40+3=43>>43 years

  Since <PERSON> age is four years more than <PERSON>''s, <PERSON> is currently 40+4
  = 44 years.

  In three years, <PERSON> will be 44+3 = <<44+3=47>>47 years old

  The total age of <PERSON> and <PERSON> in three years will be 47+43 = <<47+43=90>>90

  If the total age of <PERSON>, <PERSON> and <PERSON> in three years is 150, then <PERSON>''s
  age in three years will be 150-90 = <<150-90=60>>60

  #### 60


  Question: <PERSON><PERSON> has three times the number of counters and four times the number
  of marbles as <PERSON>. If <PERSON> has 40 counters and 50 marbles, calculate the number
  of counters and marbles that <PERSON><PERSON> has?

  Answer: If <PERSON> has 40 counters, and <PERSON><PERSON> has three, the number of counters <PERSON>
  has, <PERSON><PERSON> has 3*40 = <<40*3=120>>120 counters.

  <PERSON><PERSON> also has four times the number of marbles that <PERSON> has, meaning he has 4*50
  = <<4*50=200>>200 marbles.

  In total, <PERSON><PERSON> has 120 counters + 200 marbles = <<120+200=320>>320 items.

  #### 320


  Question: <PERSON> found 214 seashells, <PERSON> found 432 seashells, and Benny found 86
  seashells on the beach. When they cleaned them, they discovered that 67 were cracked.
  How many good seashells did they find together?

  Answer:'
question: <PERSON> found 214 seashells, <PERSON> found 432 seashells, and <PERSON> found 86 seashells
  on the beach. When they cleaned them, they discovered that 67 were cracked. How
  many good seashells did they find together?
samples:
- ' 214 + 432 + 86 = <<214+432+86=732>>732 seashells in total

  After cleaning, they found 67 cracked seashells. So, 732 - 67 = <<732-67=665>>665
  good seashells.

  #### 665


  '
- ' To find the number of good seashells, we first need to find the total number of
  seashells they found, and then subtract the number of cracked seashells.

  Tom found 214 seashells, Nancy found 432 seashells, and Benny found 86 seashells.
  In total, they found 214 + 432 + 86 = <<214+432+86=732>>732 seashells.

  Of these, 67 were cracked. So there are 732 - 67 = <<732-67=665>>665 good seashells.

  #### 665


  '
- ' The total number of seashells is 214 + 432 + 86 = <<214+432+86=732>>732

  67 of them were cracked, so the number of good seashells is 732 - 67 = <<732-67=665>>665

  #### 665


  '
- ' The total number of seashells is 214 + 432 + 86 = <<214+432+86=732>>732 seashells

  Since 67 were cracked, the number of good seashells is 732 - 67 = <<732-67=665>>665

  #### 665


  '
- ' In total, Tom, Nancy and Benny found 214 + 432 + 86 = <<214+432+86=732>>732 seashells.

  After cleaning, 67 were cracked, which means 732 - 67 = <<732-67=665>>665 seashells
  were good.

  #### 665


  '
- ' Tom found 214 seashells, Nancy found 432, and Benny found 86, so the total number
  of seashells they found is 214+432+86 = <<214+432+86=732>>732.

  After cleaning, 67 were cracked, so the number of good seashells is 732-67 = <<732-67=665>>665.

  #### 665


  '
- ' In total, the three friends found 214+432+86 = <<214+432+86=732>>732 seashells.

  After cleaning, they found 732 - 67 = <<732-67=665>>665 good seashells.

  #### 665


  '
- ' First, we calculate the total number of seashells they found. 214 + 432 + 86 =
  <<214+432+86=732>>732

  Now, we subtract the number of cracked seashells from the total. 732 - 67 = <<732-67=665>>665

  #### 665


  '
- ' First, we need to add the number of seashells each person found: 214 + 432 + 86
  = <<214+432+86=732>>732

  After cleaning, 67 seashells were cracked, meaning 732 - 67 = <<732-67=665>>665
  seashells were good.

  #### 665


  '
- ' The total number of seashells Tom, Nancy and Benny found was 214 + 432 + 86 =
  <<214+432+86=732>>732

  However, 67 of them were cracked. So, the number of good seashells they found was
  732 - 67 = <<732-67=665>>665.

  #### 665


  '
