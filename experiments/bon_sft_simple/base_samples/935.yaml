gt_answer: 'First find how many more widgets the Widgeteer 3000 makes per hour: 60
  widgets/hour - 45 widgets/hour = <<60-45=15>>15 widgets/hour

  Then multiply that difference by the number of hours in a day to find the daily
  difference: 15 widgets/hour * 24 hours/day = <<360=360>>360 widgets/day

  Then multiply the daily difference by the number of days in a month to find the
  monthly difference: 360 widgets/day * 30 days/month = <<360*30=10800>>10,800 widgets/month

  Then multiply that number by the cost per widget to find the total earnings difference:
  10,800 widgets/month * $6/widget = $<<10800*6=64800>>64,800

  #### 64800'
prompt: 'Question: <PERSON><PERSON> has a pizza that is cut into 16 slices. After she and her
  friends finish eating, there are 4 slices left. If each of them ate 2 slices of
  pizza, how many people ate the pizza?

  Answer: 16 - 4 = <<16-4=12>>12 slices of pizza were eaten.

  So there were 12/2 = <<12/2=6>>6 people eating the pizza.

  #### 6


  Question: <PERSON> wants to fly to Australia. The regular tickets cost about $2000.
  As <PERSON> is a student, he will get a 30% discount on this price. How much does
  he need to pay for his ticket?

  Answer: The discount on the ticket is 30/100 * $2000 = $<<30/100*2000=600>>600.

  So Travis needs to pay $2000 - $600 = $<<2000-600=1400>>1400.

  #### 1400


  Question: Fern is trying to decide between buying the Widgeteer 3000, which makes
  60 widgets an hour, and the WidgetMaster 9000, which makes 45 widgets an hour. Each
  widget sells for $6. How much more money will Fern make from the Widgeteer 3000
  vs. the WidgetMaster 9000 if it runs 24 hours a day for a month with 30 days?

  Answer:'
question: Fern is trying to decide between buying the Widgeteer 3000, which makes
  60 widgets an hour, and the WidgetMaster 9000, which makes 45 widgets an hour. Each
  widget sells for $6. How much more money will Fern make from the Widgeteer 3000
  vs. the WidgetMaster 9000 if it runs 24 hours a day for a month with 30 days?
samples:
- ' The Widgeteer 3000 makes 60 widgets an hour. In a day, it will make 60 * 24 =
  1440 widgets. In a month, it will make 1440 * 30 = 43200 widgets. At $6 each, it
  will make 43200 * 6 = $<<43200*6=259200>>259200.

  The WidgetMaster 9000 makes 45 widgets an hour. In a day, it will make 45 * 24 =
  1080 widgets. In a month, it will make 1080 * 30 = 32400 widgets. At $6 each, it
  will make 32400 * 6 = $<<32400*6=194400>>194400.

  The Widgeteer 3000 will make $259200 - $194400 = $<<259200-194400=64800>>64800 more
  than the WidgetMaster 9000.

  #### 64800


  '
- ' The Widgeteer 3000 will make 60 widgets an hour * 24 hours a day * 30 days = 43,200
  widgets in a month.

  The WidgetMaster 9000 will make 45 widgets an hour * 24 hours a day * 30 days =
  40,800 widgets in a month.

  So the Widgeteer 3000 will make 43,200 - 40,800 = 2,400 more widgets in a month.

  Each widget sells for $6, so Fern will make $6 * 2,400 = $<<6*2400=14,400>>14,400
  more.

  #### 14,400


  '
- ' The Widgeteer 3000 will make 60 widgets/hour * 24 hours/day * 30 days = 43,200
  widgets.

  The WidgetMaster 9000 will make 45 widgets/hour * 24 hours/day * 30 days = 32,400
  widgets.

  The difference is 43,200 - 32,400 = 10,800 widgets.

  Each widget sells for $6, so Fern will make 10,800 * $6 = $<<10800*6=64,800>>64,800
  more with the Widgeteer 3000.

  #### 64,800


  '
- ' In one hour, the Widgeteer 3000 makes 60 widgets, and the WidgetMaster 9000 makes
  45 widgets.

  So in 24 hours, the Widgeteer 3000 makes 60 x 24 = 1440 widgets, and the WidgetMaster
  9000 makes 45 x 24 = 1080 widgets.

  In a month, the Widgeteer 3000 makes 1440 x 24 x 30 = 2073600 widgets, and the WidgetMaster
  9000 makes 1080 x 24 x 30 = 1244160 widgets.

  So the Widgeteer 3000 makes 2073600 - 1244160 = 832400 more widgets.

  Each widget sells for $6, so the Widgeteer 3000 makes $832400 x $6 = $5,004,400
  more than the WidgetMaster 9000.

  #### 5004400


  '
- ' The Widgeteer 3000 makes 60 widgets an hour, and 60 * 24 = 1440 widgets a day.
  For 30 days, it will make 1440 * 30 = 43200 widgets.

  Each widget sells for $6, so the Widgeteer 3000 will make 43200 * $6 = $<<43200*6=259200>>259200.

  The WidgetMaster 9000 makes 45 widgets an hour, and 45 * 24 = 1080 widgets a day.
  For 30 days, it will make 1080 * 30 = 32400 widgets.

  Each widget sells for $6, so the WidgetMaster 9000 will make 32400 * $6 = $<<32400*6=194400>>194400.

  The Widgeteer 3000 will make $259200 - $194400 = $<<259200-194400=64800>>64800 more.

  #### 64800


  '
- ' 60 widgets/hour * 24 hours = 1440 widgets/day * 30 days = 43,200 widgets.

  45 widgets/hour * 24 hours = 1080 widgets/day * 30 days = 32,400 widgets.

  The difference in the number of widgets produced is 43,200 - 32,400 = 10,800 widgets.

  10,800 widgets * $6/widget = $<<10800*6=64,800>>64,800.

  #### 64,800


  '
- ' The Widgeteer 3000 will make 60 widgets per hour. In 24 hours, it will make 60
  * 24 = 1440 widgets.

  In a month, it will make 1440 * 30 = 43200 widgets.

  At $6 per widget, it will make 43200 * $6 = $<<43200*6=259200>>$259200.

  The WidgetMaster 9000 will make 45 widgets per hour. In 24 hours, it will make 45
  * 24 = 1080 widgets.

  In a month, it will make 1080 * 30 = 32400 widgets.

  At $6 per widget, it will make 32400 * $6 = $<<32400*6=194400>>$194400.

  The Widgeteer 3000 will make $259200 - $194400 = $<<259200-194400=64800>>$64800
  more than the WidgetMaster 9000.

  #### 64800


  '
- ' The Widgeteer 3000 will make 60 widgets/hour * 24 hours/day * 30 days = 43,200
  widgets.

  The WidgetMaster 9000 will make 45 widgets/hour * 24 hours/day * 30 days = 41,400
  widgets.

  The difference is 43,200 - 41,400 = 1800 widgets.

  Each widget sells for $6, so Fern will make $1800 * $6 = $<<1800*6=10,800>>10,800
  more.

  #### 10,800


  '
- ' The Widgeteer 3000 makes 60 widgets/hour. In 24 hours, it makes 60 * 24 = 1440
  widgets. In 30 days, it makes 1440 * 30 = 43200 widgets. It makes $6 each, so it
  makes 43200 * $6 = $<<43200*6=259200>>$259200.

  The WidgetMaster 9000 makes 45 widgets/hour. In 24 hours, it makes 45 * 24 = 1080
  widgets. In 30 days, it makes 1080 * 30 = 32400 widgets. It makes $6 each, so it
  makes 32400 * $6 = $<<32400*6=194400>>$194400.

  The Widgeteer 3000 makes $259200 - $194400 = $<<259200-194400=64800>>$64800 more
  than the WidgetMaster 9000.

  #### 64800


  '
- ' First we need to find how many widgets each machine makes in a day. The Widgeteer
  3000 makes 60 widgets per hour. There are 24 hours in a day. So, it makes 60 * 24
  = 1440 widgets in a day. The WidgetMaster 9000 makes 45 widgets per hour. So, it
  makes 45 * 24 = 1080 widgets in a day.

  Now, we need to find how many widgets each machine makes in a month. There are 30
  days in a month. So, the Widgeteer 3000 makes 1440 * 30 = 43200 widgets in a month.
  The WidgetMaster 9000 makes 1080 * 30 = 32400 widgets in a month.

  Now, we need to find how much money each machine makes in a month. Each widget sells
  for $6. So, the Widgeteer 3000 makes 43200 * 6 = $259200. The WidgetMaster 9000
  makes 32400 * 6 = $194400.

  Now, we need to find the difference in money made. The Widgeteer 3000 makes $259200
  - $194400 = $<<259200-194400=64600>>64600 more than the WidgetMaster 9000.

  #### 64600


  '
