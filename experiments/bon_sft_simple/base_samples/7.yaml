gt_answer: 'First find how many gigabytes are in 40% of the file: 200 GB * 40% = <<200*40*.01=80>>80
  GB

  Then divide that number by the download rate to find the time until Windows restarts:
  80 GB / 2 GB/minute = <<80/2=40>>40 minutes

  Then find the time to download the whole file after the restart: 200 GB / 2 GB/minute
  = <<200/2=100>>100 minutes

  Then add the time to download 40% of the file, to download the whole file, and to
  wait for Windows to update: 40 minutes + 100 minutes + 20 minutes = <<40+100+20=160>>160
  minutes

  #### 160'
prompt: 'Question: There are 235 books in a library. On Tuesday, 227 books are taken
  out. On Thursday, 56 books are brought back and 35 books are taken out again on
  Friday. How many books are there now?

  Answer: On Tuesday, 227 books were taken, so there are 235 - 227= <<235-227=8>>8
  books left.

  56 books were brought back on Thursday, so there are 8 + 56 = <<8+56=64>>64 books
  in the library.

  Finally, 35 books are taken out on Friday, leaving 64 - 35 = <<29=29>>29 books available.

  #### 29


  Question: <PERSON><PERSON> makes $21 of profit every time she sells 3 shirts and four times
  as much profit when she sells two pairs of sandals. How much profit will she make
  if she sells 7 shirts and 3 pairs of sandals?

  Answer: <PERSON><PERSON> makes $21 * 4 = $<<21*4=84>>84 of profit for two pairs of sandals.

  <PERSON><PERSON> makes $84 / 2 pairs = $<<84/2=42>>42 of profit per pair of shoes.

  <PERSON>ony makes $21 / 3 shirts = $<<21/3=7>>7 of profit per shirt.

  For seven shirts, Melony will make 7 shirts * $7 = $<<7*7=49>>49 of profit.

  For three pairs of sandals, <PERSON><PERSON> will make 3 pairs * $42/pair = $<<3*42=126>>126
  of profit.

  <PERSON>ony will make a total of $49 + $126 = $<<49+126=175>>175 of profit.

  #### 175


  Question: Carla is downloading a 200 GB file. Normally she can download 2 GB/minute,
  but 40% of the way through the download, Windows forces a restart to install updates,
  which takes 20 minutes. Then Carla has to restart the download from the beginning.
  How load does it take to download the file?

  Answer:'
question: Carla is downloading a 200 GB file. Normally she can download 2 GB/minute,
  but 40% of the way through the download, Windows forces a restart to install updates,
  which takes 20 minutes. Then Carla has to restart the download from the beginning.
  How load does it take to download the file?
samples:
- ' Carla is downloading a 200 GB file. 40% of 200 GB is 0.4 * 200 = 80 GB. It will
  take Carla 80 / 2 = 40 minutes to download 80 GB.

  After the restart, Carla has to download the remaining 200 - 80 = 120 GB. It will
  take Carla 120 / 2 = 60 minutes to download the remaining 120 GB.

  The total time it takes to download the file is 40 + 20 + 60 = 120 minutes.

  #### 120


  '
- ' Carla can download 2 GB/minute, so normally it would take 200 GB / 2 GB/minute
  = 100 minutes to download the file.

  However, 40% of the way through the download is 0.4 * 200 GB = 80 GB, which takes
  80 GB / 2 GB/minute = 40 minutes to download.

  The Windows restart takes 20 minutes, so the total time for the first part of the
  download is 40 + 20 = 60 minutes.

  Since Carla has to restart the download from the beginning, the total time will
  be 60 + 100 = 160 minutes.

  #### 160


  '
- ' Carla is downloading a 200 GB file, so she has to download 200 - 80 = 120 GB more.

  Carla downloads 2 GB/minute, so she will take 120 / 2 = 60 minutes to download the
  file.

  #### 60


  '
- ' Carla needs to download 200 GB. 2 GB/minute is the same as 120 GB/20 minutes,
  or 6 GB/minute.

  40% of the way through the download means she has downloaded 200 * 0.4 = 80 GB.

  Carla has 200 - 80 = 120 GB left to download. It will take her 120 / 6 = 20 minutes
  to download the remaining file.

  #### 40


  '
- ' Carla is downloading 200 GB, and it will take her 200 GB / 2 GB/minute = 100 minutes
  to download the file.

  But 40% of the way through the download, she has to stop, so she has downloaded
  0.4 * 200 GB = 80 GB.

  It will take her 20 minutes to restart her computer, so she has lost 20 minutes.

  She has to start over, so she has to download the entire file again.

  #### 120


  '
- ' Carla is downloading a 200 GB file. Normally, she can download 2 GB/minute, so
  at a normal rate, she can download 2 GB/minute * 60 minutes = 120 GB/hour.

  40% of the way through the download, she has downloaded 40% * 200 GB = 80 GB.

  She has 200 GB - 80 GB = 120 GB left to download.

  The download is interrupted, and Windows takes 20 minutes to install updates. After
  the download is restarted, Carla has to download the entire 200 GB file again.

  The time it takes to download the file is the time it takes to download the first
  80 GB + the time it takes to download the remaining 120 GB + the time it takes for
  Windows to install updates.

  The time it takes to download the first 80 GB is 80 GB / 2 GB/minute = 40 minutes.

  The time it takes to download the remaining 120 GB is 120 GB / 2 GB/minute = 60
  minutes.

  The time it takes for Windows to install updates is 20 minutes.

  The total time it takes to download the file is 40 minutes + 60 minutes + 20 minutes
  = 120 minutes.

  #### 120


  '
- ' Carla downloads at a rate of 2 GB/minute.

  40% of the way through the download is 40% * 200 GB = 80 GB.

  So Carla downloads 80 GB in 80 GB / 2 GB/minute = 40 minutes.

  Then the computer restarts and takes 20 minutes.

  So the total time for the first part of the download is 40 + 20 = 60 minutes.

  Carla has to restart the download from the beginning, so it will take her another
  200 GB / 2 GB/minute = 100 minutes.

  The total time to download the file is 60 + 100 = 160 minutes.

  #### 160


  '
- ' Carla can download 2 GB/minute. The file is 200 GB, so it will take 200 / 2 =
  100 minutes to download normally.

  When Windows forces a restart, Carla has downloaded 40% of the file. 40% of 200
  GB is 0.4 * 200 = 80 GB. The download is 80 GB, so it will take 80 / 2 = 40 minutes
  to download this part.

  After the restart, Carla has to start over from the beginning. She has to download
  200 GB again, which will take 200 / 2 = 100 minutes.

  The total time to download the file is 40 minutes (before the restart) + 20 minutes
  (for the restart) + 100 minutes (after the restart) = 160 minutes.

  #### 160


  '
- ' Carla has to download 200 GB. In the first 40% of the download, she downloads
  200 * 40% = 80 GB.

  It takes Carla 80 GB / 2 GB/minute = 40 minutes to download the first 40% of the
  file.

  After the restart, Carla has to download the remaining 120 GB (200 - 80 = 120).

  It takes Carla 120 GB / 2 GB/minute = 60 minutes to download the remaining 120 GB.

  So the total time it takes to download the file is 40 + 20 + 60 = 120 minutes.

  #### 120


  '
- ' Carla has to download 200 GB = 200,000 MB = 200,000,000 bytes.

  At a rate of 2 GB/minute, Carla can download 2,000,000,000 bytes/minute.

  Carla downloads for 40% of the file, which is 200,000,000 * 0.4 = 80,000,000,000
  bytes.

  She downloads at 2,000,000,000 bytes/minute, so it takes her 80,000,000,000 / 2,000,000,000
  = 40 minutes to download 40% of the file.

  Then she has to download the remaining 60% of the file. 60% of 200,000,000,000 bytes
  is 120,000,000,000 bytes.

  It will take her 120,000,000,000 / 2,000,000,000 = 60 minutes to download the remaining
  60% of the file.

  However, before she can start downloading the remaining 60% of the file, she has
  to wait for 20 minutes to install updates. So she waits 20 minutes and then downloads
  the remaining 60% of the file.

  It will take her 20 + 60 = 80 minutes to download the entire file.

  #### 80


  '
