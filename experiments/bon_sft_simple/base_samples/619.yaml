gt_answer: 'If the number of poles at each intersection is 6, and the road has 4 intersections,
  the total number of poles having street lights is 6*4=<<6*4=24>>24

  Since each pole on a road intersection has 4 street lights, the total number of
  streetlights in all the poles is 24*4=<<24*4=96>>96

  If 20 streetlights from the total number are not working, the number of functioning
  street lights is 96-20=<<96-20=76>>76

  #### 76'
prompt: 'Question: <PERSON> built a rectangular concrete patio that was four times
  as long as it was wide.  If the perimeter of the patio was 100 feet, what was the
  length of the patio, in feet?

  Answer: Let''s make "x" be the width of the patio.

  Then the length of the patio would be 4*x.

  And the perimeter of the patio would be the sum of all four sides, or x+(4*x)+x+(4*x)=100
  feet.

  This simplifies to 10x=100 feet.

  And the width of the patio x=<<10=10>>10 feet.

  And the length of the patio is 4*x = 4*10 = 40 feet.

  #### 40


  Question: <PERSON> bought 4 boxes of crackers. Each box contained 24 crackers. If
  <PERSON> bought one box less than twice as many boxes of crackers as <PERSON>, how many
  crackers did they buy altogether?

  Answer: If <PERSON> bought 4 boxes of crackers, each containing 24 crackers, the total
  number of crackers that <PERSON> bought is 24*4=<<24*4=96>>96

  Twice as many boxes of crackers as <PERSON> bought is 2*4=<<2*4=8>>8

  If <PERSON> bought one box less than twice as many boxes of crackers as <PERSON>, then
  <PERSON> bought 8-1=<<8-1=7>>7 boxes of crackers.

  The total number of crackers that <PERSON> bought is 7*24=<<7*24=168>>168

  Together, <PERSON> and Clavin bought 168+96=<<168+96=264>>264 crackers.

  #### 264


  Question: Each pole on a road intersection has 4 street lights. If the number of
  poles at each intersection is 6, and the road has 4 intersections, calculate the
  total number of functioning street lights if 20 streetlights from the total number
  are not working.

  Answer:'
question: Each pole on a road intersection has 4 street lights. If the number of poles
  at each intersection is 6, and the road has 4 intersections, calculate the total
  number of functioning street lights if 20 streetlights from the total number are
  not working.
samples:
- ' If there are 6 poles at each intersection, and there are 4 intersections, then
  the total number of poles is 4*6=<<4*6=24>>24

  Each pole has 4 street lights, so the total number of street lights is 24*4=<<24*4=96>>96

  If 20 streetlights are not working, then the total number of functioning street
  lights is 96-20=<<96-20=76>>76

  #### 76


  '
- ' Each pole has 4 streetlights, so at each intersection there are 6*4=<<6*4=24>>24
  streetlights. Since there are 4 intersections, there are 4*24=<<4*24=96>>96 streetlights.

  But 20 streetlights are not working, so the total number of functioning streetlights
  is 96-20=<<96-20=76>>76.

  #### 76


  '
- ' Each pole has 4 street lights. And there are 6 poles at each intersection. So
  each intersection has 6*4=<<6*4=24>>24 street lights.

  And there are 4 intersections. So the total number of street lights is 24*4=<<24*4=96>>96

  20 streetlights are not working. So the total number of functioning street lights
  is 96-20=<<96-20=76>>76

  #### 76


  '
- ' Each pole has 4 street lights, and there are 6 poles at each intersection. The
  total number of street lights at each intersection is 6*4=<<6*4=24>>24.

  There are 4 intersections, so the total number of functioning street lights is 4*24=<<4*24=96>>96.

  If 20 street lights are not functioning, the total number of functioning street
  lights is 96-20=<<96-20=76>>76.

  #### 76


  '
- ' At each intersection, there are 6 poles. Each pole has 4 street lights. So, at
  each intersection, there are 6*4=<<6*4=24>>24 street lights.

  At 4 intersections, there are 24*4=<<24*4=96>>96 street lights.

  But 20 street lights are not working. So, the total number of functioning street
  lights is 96-20=<<96-20=76>>76.

  #### 76


  '
- ' There are 4 street lights on each pole, and 6 poles at each intersection. So,
  there are 4*6=<<4*6=24>>24 street lights at each intersection.

  There are 4 intersections, so the total number of street lights is 4*24=<<4*24=96>>96

  20 street lights are not working, so there are 96-20=<<96-20=76>>76 street lights
  that are working.

  #### 76


  '
- ' The total number of functioning street lights is the total number of street lights
  minus the number of street lights that are not working.

  The total number of street lights is 6*4=<<6*4=24>>24

  The total number of functioning street lights is 24-20=<<24-20=4>>4

  #### 4


  '
- ' 6 poles at each intersection, and there are 4 intersections, so there are 6*4=24
  poles.

  Each pole has 4 street lights, so there are 24*4=<<24*4=96>>96 streetlights.

  20 streetlights are not working, so the total number of functioning street lights
  is 96-20=<<96-20=76>>76.

  #### 76


  '
- ' Each pole has 4 streetlights. There are 6 poles at each intersection. So the total
  number of streetlights at each intersection is 6*4=<<6*4=24>>24

  The road has 4 intersections. So the total number of streetlights is 4*24=<<4*24=96>>96

  20 streetlights are not working. So the total number of functioning street lights
  is 96-20=<<96-20=76>>76

  #### 76


  '
- ' Each pole has 4 street lights. 6 poles means there are 6*4=<<6*4=24>>24 street
  lights at one intersection. Since there are 4 intersections, the total number of
  street lights is 4*24=<<4*24=96>>96. But 20 street lights are not working, so the
  total number of functioning street lights is 96-20=<<96-20=76>>76.

  #### 76


  '
