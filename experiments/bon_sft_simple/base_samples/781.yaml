gt_answer: 'The other two pizzas cost 64-30 = <<64-30=34>>34 dollars.

  Each of the other two pizzas cost 34/2 = <<34/2=17>>17 dollars each.

  #### 17'
prompt: 'Question: <PERSON> read 23 books, <PERSON> read 12 books and <PERSON><PERSON><PERSON> read 17 books.
  <PERSON> and <PERSON> read 3 of the same books and all three had read the same book as well.
  In total how many different books have <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> read?

  Answer: <PERSON> and <PERSON> together read 23 + 12 = <<23+12=35>>35 books

  3 of these books were duplicates so <PERSON> and <PERSON> together read 35 - 3 = <<35-3=32>>32
  unique books

  All three read 32 + 17 = <<32+17=49>>49 books

  Because the books are being counted three times, the book is counted 3 - 1 = <<3-1=2>>2
  extra times.

  All together they have read 49 - 2 = <<49-2=47>>47 different books

  #### 47


  Question: <PERSON> has a chicken farm with 4 chickens. His chickens lay 3 eggs each
  day. If <PERSON> collects all the eggs after 3 days, then how many eggs will <PERSON> have
  collected?

  Answer: Each chicken lays 3 eggs per day and there are 4 chickens 3 * 4 = <<3*4=12>>12

  He will collect all the eggs after 3 days 12 * 3 = <<12*3=36>>36

  #### 36


  Question: Four friends ordered four pizzas for a total of 64 dollars. If two of
  the pizzas cost 30 dollars, how much did each of the other two pizzas cost if they
  cost the same amount?

  Answer:'
question: Four friends ordered four pizzas for a total of 64 dollars. If two of the
  pizzas cost 30 dollars, how much did each of the other two pizzas cost if they cost
  the same amount?
samples:
- ' Two of the pizzas cost 30 dollars

  The other two pizzas cost 64 - 30 = <<64-30=34>>34

  The other two pizzas cost the same amount, so each of them cost 34 / 2 = 17 dollars

  #### 17


  '
- ' Two of the pizzas cost 30 dollars. The other two pizzas cost 64 - 30 = 34 dollars.
  The two pizzas cost the same amount. So we can divide the 34 by 2. 34 / 2 = 17

  #### 17


  '
- ' 2 pizzas cost 30 dollars, so the remaining 2 pizzas cost 64 - 30 = 34 dollars

  The cost per pizza is 34 / 2 = 17

  #### 17


  '
- ' The total cost of the pizzas is 64 dollars. 2 of the pizzas cost 30 dollars. 30
  + 30 = <<30+30=60>>60

  This leaves 64 - 60 = <<64-60=4>>4 dollars for the remaining 2 pizzas

  If the remaining pizzas cost the same amount, then they cost 4 / 2 = <<4/2=2>>2
  dollars each

  #### 2


  '
- ' Two of the pizzas cost 30 dollars. 64 - 30 = <<64-30=34>>34

  The other two pizzas cost 34 dollars. 34 / 2 = <<34/2=17>>17

  #### 17


  '
- ' The total cost is 64 dollars. Two pizzas cost 30 dollars, so the remaining pizzas
  cost 64 - 30 = <<64-30=34>>34

  The remaining pizzas cost the same amount so we divide 34 by 2 to get 17.

  #### 17


  '
- ' 64 - 30 = <<64-30=34>>34

  Each of the other two pizzas cost 34 / 2 = <<34/2=17>>17

  #### 17


  '
- ' The total cost of the pizzas is 64 dollars. The cost of two of the pizzas is 30
  dollars. The cost of the remaining two pizzas is 64 - 30 = <<64-30=34>>34 dollars.

  Since they cost the same, each pizza costs 34 / 2 = <<34/2=17>>17 dollars.

  #### 17


  '
- ' Two pizzas cost 30 dollars. So the other two pizzas cost 64 - 30 = <<64-30=34>>34

  The other two pizzas cost the same amount, so divide the cost by 2 34 / 2 = <<34/2=17>>17

  #### 17


  '
- ' The total cost of the pizzas is 64 dollars. Two of the pizzas cost 30 dollars.
  The total cost of the other two pizzas is 64 - 30 = <<64-30=34>>34 dollars

  If they cost the same amount, then each of the other two pizzas costs 34 / 2 = <<34/2=17>>17
  dollars.

  #### 17


  '
