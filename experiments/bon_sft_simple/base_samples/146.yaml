gt_answer: 'The first set had 500 pieces, and the second set had 3 times as many as
  the first set, so the second set has 500*3=<<500*3=1500>>1500 pieces

  The third set had one-quarter of the 500 pieces, so that means it had 500*.25= <<500*.25=125>>125
  pieces

  In total, that means <PERSON> picked up 500 + 1500+ 125= <<500+1500+125=2125>>2125
  blocks

  #### 2,125'
prompt: 'Question: <PERSON> is saving his money to buy a brand new bike that costs $100.  His
  weekly allowance is $5.  His parent will pay him an extra $10 to mow the lawn.  His
  neighbor will pay him $7 per hour to babysit their son.  He has already saved up
  $65.  He''ll receive his allowance on Friday and he''s planning on babysitting for
  2 hours this Saturday after he mows the lawn.  How much more money does <PERSON> need
  to earn before he can buy the bike?

  Answer: If he babysits for 2 hours at $7 per hour, he will earn 2*7 = $<<2*7=14>>14

  This week he will earn $5 allowance, $10 mowing the lawn and $14 from babysitting
  for a total of 5+10+14 = $<<5+10+14=29>>29

  If we add the $29 he will earn to his $65 savings, he will have a total of 29 +
  65 = $<<29+65=94>>94

  The bike costs $100 and he will have $94 leaving $100-$94 = $<<100-94=6>>6 more
  that he will need to earn

  #### 6


  Question: Summer and Jolly both went to the same middle school. However, when they
  finished college, Summer had five more degrees than Jolly. If Summer has a total
  of 150 degrees, what''s the combined number of degrees they both have?

  Answer: Jolly has 150 - 5 = <<150-5=145>>145 degrees

  Since Summer has 150 degrees, the combined total for both is 150+145 = <<150+145=295>>295

  #### 295


  Question: Johnny is picking up the toys on the floor of his room.  He''d dumped
  a lego boxed set with 500 pieces on the floor, and another one that had 3 times
  more pieces than the 500 piece one, and another one that had 1/4 the number of pieces.  How
  many blocks does Johnny pick up if he picks up all the legos?

  Answer:'
question: Johnny is picking up the toys on the floor of his room.  He'd dumped a lego
  boxed set with 500 pieces on the floor, and another one that had 3 times more pieces
  than the 500 piece one, and another one that had 1/4 the number of pieces.  How
  many blocks does Johnny pick up if he picks up all the legos?
samples:
- ' 500 + 3*500 + 500/4 = 500 + 1500 + 125 = 2125

  #### 2125


  '
- ' The first box has 500 pieces.  The second box has 3 times more pieces than the
  first, so it has 3*500 = 1500 pieces.  The third box has 1/4 the number of pieces
  of the first box, so it has 500/4 = 125 pieces.

  Adding these up, we have 500 + 1500 + 125 = 2125

  #### 2125


  '
- ' The 500 piece lego set has 500 pieces. The 3 times more lego set has 3*500 = 1500
  pieces. The 1/4 the number of pieces lego set has 1/4 * 500 = 125 pieces.

  Adding all the pieces together, we get 500 + 1500 + 125 = 2125

  #### 2125


  '
- ' There were 500 pieces in the first box. The second box had 3 times more, which
  is 500*3 = 1500 pieces. The third box had 1/4 the number of pieces, which is 500/4
  = 125 pieces.

  So, in total, there were 500 + 1500 + 125 = 2125 pieces

  #### 2125


  '
- ' The 500 piece set has 500 pieces.  The second set has 3 times more, so it has
  500*3 = 1500 pieces.  The third set has 1/4 the number of pieces, so it has 500/4
  = 125 pieces.  The total number of pieces is 500 + 1500 + 125 = 2125

  #### 2125


  '
- ' The second box has 3 * 500 = 1500 pieces

  The third box has 1/4 * 500 = 125 pieces

  Adding up the number of pieces, we get 500 + 1500 + 125 = 2125

  #### 2125


  '
- ' The second box has 3 * 500 = 1500 pieces.  The third box has 1/4 * 500 = 125 pieces.  If
  we add all the pieces together, we have 500 + 1500 + 125 = 2125 pieces.

  #### 2125


  '
- ' The 500 piece box had 500 pieces. The other one had 3 times as many, so 3*500
  = 1500 pieces. The last box had 1/4 the number, so 500/4 = 125 pieces. In total,
  Johnny picks up 500 + 1500 + 125 = 2125 pieces.

  #### 2125


  '
- ' The first box has 500 pieces. The second one has 3 times more, so 3 x 500 = 1500
  pieces. The third one has 1/4 the number of pieces, so 1/4 x 500 = 125 pieces. In
  total, Johnny picks up 500 + 1500 + 125 = 2125 pieces.

  #### 2125


  '
- ' The second box had 3 times more pieces, so it had 3 * 500 = 1500 pieces. The third
  box had 1/4 the number of pieces, so it had 1/4 * 500 = 125 pieces.

  In total, he picked up 500 + 1500 + 125 = <<500+1500+125=2125>>2125

  #### 2125


  '
