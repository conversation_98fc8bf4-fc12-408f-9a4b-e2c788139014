gt_answer: 'Since <PERSON> was losing 10 pounds each month, in 3 months he lost 10 * 3
  = <<10*3=30>>30 pounds.

  Adding the weight lost to the final weight gives an initial weight of 70 + 30 =
  <<70+30=100>>100 pounds.

  Mark''s initial weight was 100 pounds.

  #### 100'
prompt: 'Question: <PERSON> is buying asphalt to pave a new section of road. The road
  will be 2000 feet long and 20 feet wide. Each truckload of asphalt will cover 800
  square feet of road. If each truckload costs $75, and there''s a 20% sales tax,
  how much will <PERSON> need to pay for asphalt?

  Answer: First, calculate how many square feet of road Mark needs to cover by multiplying
  the length by the width: 2000 feet * 20 feet = <<2000*20=40000>>40,000 sq ft

  Then divide the square footage of road by the number of square feet each truckload
  will cover: 40,000 sq ft / 800 sq ft/truckload = <<40000/800=50>>50 truckloads

  Now multiply the number of truckloads by the cost per truckload: $75/truckload *
  50 truckloads = $<<75*50=3750>>3750

  Now calculate the sales tax by multiplying $3750 by 20%: $3750 * .2 = $<<3750*.2=750>>750

  Now add the sales tax to the cost of the asphalt to find the total: $750 + $3750
  = $<<750+3750=4500>>4500

  #### 4500


  Question: The region east of plain A is 50 square miles less than the region east
  of plain B. If plain B has 200 square miles, how many square miles do the plains
  have in total?

  Answer: The region of plain A has a total of 200 - 50 = <<200-50=150>>150 square
  miles.

  The total number of square miles for both plains is 150 square miles for plain A
  + 200 square miles for plain B = <<150+200=350>>350 square miles

  #### 350


  Question: Mark was unwell for 3 months, during which he lost 10 pounds per month.
  If his final weight was 70 pounds, what was his initial weight?

  Answer:'
question: Mark was unwell for 3 months, during which he lost 10 pounds per month.
  If his final weight was 70 pounds, what was his initial weight?
samples:
- ' Mark lost 10 pounds each month for 3 months, so he lost a total of 10 * 3 = <<10*3=30>>30
  pounds.

  If his final weight was 70 pounds, then his initial weight was 70 + 30 = <<70+30=100>>100
  pounds.

  #### 100


  '
- ' Mark lost 10 pounds each month for 3 months. So he lost a total of 10 * 3 = 30
  pounds.

  His final weight was 70 pounds, so his initial weight was 70 + 30 = 100 pounds.

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months. So he lost a total of 10 * 3 = <<10*3=30>>30
  pounds.

  His initial weight was 70 + 30 = <<70+30=100>>100 pounds.

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months, so he lost a total of 10 * 3 = <<10*3=30>>30
  pounds.

  His initial weight was 70 + 30 = <<70+30=100>>100 pounds

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months, so he lost a total of 10 * 3 = <<10*3=30>>30
  pounds.

  His final weight was 70 pounds, so his initial weight was 70 + 30 = <<70+30=100>>100
  pounds.

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months. 10 pounds per month * 3 months = <<10*3=30>>30
  pounds.

  So, Mark lost 30 pounds. His final weight is 70 pounds, so his initial weight was
  70 pounds + 30 pounds = <<70+30=100>>100 pounds.

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months, so he lost a total of 10 * 3 = 30
  pounds.

  His final weight is 70 pounds, so his initial weight was 70 + 30 = 100 pounds.

  #### 100


  '
- ' Mark lost 10 pounds each month for 3 months. So he lost 3 * 10 = 30 pounds. If
  his final weight is 70 pounds, then his initial weight was 70 + 30 = 100 pounds

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months. So he lost a total of 10 * 3 = <<10*3=30>>30
  pounds.

  His final weight is 70 pounds, so his initial weight must have been 70 + 30 = <<70+30=100>>100
  pounds.

  #### 100


  '
- ' Mark lost 10 pounds per month for 3 months, so he lost 3 * 10 = 30 pounds.

  His initial weight was 70 pounds + 30 pounds = <<70+30=100>>100 pounds

  #### 100


  '
