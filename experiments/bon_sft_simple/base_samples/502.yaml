gt_answer: 'Two of Cherries friends in total want 2*5=<<2*5=10>>10 gifts.

  Her other three friends will get 3*2=<<3*2=6>>6 gifts in total between them.

  Ch<PERSON><PERSON> buys more gifts, leaving a total of 10+6+10=<<10+6+10=26>>26 gifts.

  #### 26'
prompt: 'Question: <PERSON><PERSON>''s monthly salary is $576. If he works 8 hours a day for
  6 days a week, how much is his hourly rate?

  Answer: In a week, <PERSON><PERSON>''s salary is $576/4 = $<<576/4=144>>144.

  In a day, <PERSON><PERSON> earns $144/6 = $<<144/6=24>>24.

  Thus, his hourly rate is $24/8 = $<<24/8=3>>3.

  #### 3


  Question: There are 250 books inside a library. On Tuesday, 120 books are taken
  out to be read by children. On Wednesday, 35 books are returned. On Thursday, another
  15 books are withdrawn from the library. How many books are now in the library?

  Answer: On Tuesday, 120 books were taken out 250 - 120 = <<250-120=130>>130 books

  On Wednesday, 35 books were returned 130 + 35 = <<130+35=165>>165 books

  On Thursday, 15 books were borrowed 165 - 15 = 150 books

  #### 150


  Question: Ch<PERSON>rie wants to buy Christmas gifts for her 5 friends. 2 of her friends
  want 5 gifts and the other 3 friends want 2 gifts for themselves. <PERSON><PERSON><PERSON> goes ahead
  and buys them and decides to further surprise them by getting 10 more random gifts.
  How many gifts did Cherrie buy in total?

  Answer:'
question: Cherrie wants to buy Christmas gifts for her 5 friends. 2 of her friends
  want 5 gifts and the other 3 friends want 2 gifts for themselves. Cherrie goes ahead
  and buys them and decides to further surprise them by getting 10 more random gifts.
  How many gifts did Cherrie buy in total?
samples:
- ' Cherrie bought 2 friends 5 gifts each, which is 2 x 5 = 10 gifts.

  She bought 3 friends 2 gifts each, which is 3 x 2 = 6 gifts.

  Adding these to the 10 random gifts, she bought a total of 10 + 6 + 10 = 26 gifts.

  #### 26


  '
- ' 2 friends want 5 gifts each, so Cherrie buys 5 + 5 = 10 gifts for them.

  3 friends want 2 gifts each, so Cherrie buys 2 + 2 + 2 = 6 gifts for them.

  Cherrie buys 10 more random gifts.

  In total, Cherrie bought 10 + 6 + 10 = 26 gifts.

  #### 26


  '
- ' Cherrie bought 2 friends 5 gifts each, so she bought 2 * 5 = 10 gifts. She bought
  3 friends 2 gifts each, so she bought 3 * 2 = 6 gifts. 10 + 6 = 16 gifts. Then she
  bought 10 more gifts, so in total she bought 16 + 10 = 26 gifts.

  #### 26


  '
- ' Cherrie bought 2 gifts for 2 friends, 2 gifts for 3 friends and 10 random gifts.

  2 * 5 = 10 gifts, 3 * 2 = 6 gifts, 10 random gifts

  In total, Cherrie bought 10 + 6 + 10 = 26 gifts.

  #### 26


  '
- ' For 2 friends, Cherrie buys 2 x 5 = 10 gifts

  For 3 friends, Cherrie buys 3 x 2 = 6 gifts

  In total, Cherrie buys 10 + 6 = 16 gifts

  And she also buys 10 random gifts

  So, in total Cherrie buys 16 + 10 = 26 gifts

  #### 26


  '
- ' 2 friends want 5 gifts, so Cherrie buys 2 x 5 = 10 gifts for them.

  3 friends want 2 gifts, so Cherrie buys 3 x 2 = 6 gifts for them.

  10 more random gifts are also bought. In total, Cherrie bought 10 + 6 + 10 = 26
  gifts.

  #### 26


  '
- ' Cherrie bought 2 gifts x 5 = 10 gifts for 2 friends and 3 gifts x 3 = 9 gifts
  for 3 friends. In total, she bought 10 + 9 = 19 gifts.

  Then, she bought 10 more random gifts. 19 + 10 = 29 gifts.

  #### 29


  '
- ' Cherrie bought 5 gifts for 2 friends, which is 2 x 5 = 10 gifts.

  She bought 2 gifts for 3 friends, which is 3 x 2 = 6 gifts.

  10 more random gifts were added, making it 10 + 6 + 10 = 26 gifts.

  #### 26


  '
- ' 2 friends want 5 gifts each, so 2 x 5 = 10 gifts. 3 friends want 2 gifts each,
  so 3 x 2 = 6 gifts. 10 more random gifts were bought, so in total, 10 + 6 + 10 =
  26 gifts were bought.

  #### 26


  '
- ' Cherrie bought 2 friends 5 gifts each, so 2 * 5 = 10 gifts.

  She bought 3 friends 2 gifts each, so 3 * 2 = 6 gifts.

  In total, she bought 10 + 6 = 16 gifts.

  Then, she bought 10 more random gifts.

  In total, Cherrie bought 16 + 10 = 26 gifts.

  #### 26


  '
