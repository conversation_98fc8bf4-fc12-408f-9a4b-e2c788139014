config:
  N: 10
  batch_size: 4
  gradient_accumulation_steps: 16
  lambda_N: 0.1
  learning_rate: 2.0e-05
  log_interval: 5
  lora_alpha: 64
  lora_r: 32
  max_seq_length: 1024
  model: meta-llama/Meta-Llama-3-8B-Instruct
  num_epochs: 3
  samples_dir: !!python/object/apply:pathlib.PosixPath
  - /
  - home
  - yekai
  - BoN
  - experiments
  - bon_sft_simple
  - base_eval
  save_dir: !!python/object/apply:pathlib.PosixPath
  - /
  - home
  - yekai
  - BoN
  - experiments
  - bon_sft_simple
  - training
  save_every: 1
  warmup_steps: 50
final_step: 6
model: meta-llama/Meta-Llama-3-8B-Instruct
num_epochs: 3
num_samples_trained: 100
samples_dir: /home/<USER>/BoN/experiments/bon_sft_simple/base_eval
