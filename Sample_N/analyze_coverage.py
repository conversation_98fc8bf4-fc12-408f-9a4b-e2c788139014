#!/usr/bin/env python3

import yaml
import os
from collections import defaultdict
import sys

def analyze_coverage(directory_path, dataset_name):
    """Analyze coverage statistics for evaluation YAML files."""
    print(f"\n{'='*60}")
    print(f"Analyzing {dataset_name} dataset")
    print(f"Directory: {directory_path}")
    print(f"{'='*60}")
    
    total_questions = 0
    questions_with_coverage = 0
    true_count_distribution = defaultdict(int)
    
    # Get all YAML files
    yaml_files = [f for f in os.listdir(directory_path) if f.endswith('.yaml')]
    total_files = len(yaml_files)
    
    print(f"Total YAML files found: {total_files}")
    
    # Process each file
    for i, filename in enumerate(yaml_files):
        if i % 100 == 0:
            print(f"Processing file {i}/{total_files}...", end='\r')
            
        filepath = os.path.join(directory_path, filename)
        
        try:
            with open(filepath, 'r') as f:
                data = yaml.safe_load(f)
                
            if 'is_corrects' in data:
                total_questions += 1
                is_corrects = data['is_corrects']
                
                # Count number of True values
                true_count = sum(1 for correct in is_corrects if correct is True)
                
                # Check if any value is True (has coverage)
                if any(is_corrects):
                    questions_with_coverage += 1
                
                # Track distribution
                true_count_distribution[true_count] += 1
                
        except Exception as e:
            print(f"\nError processing {filename}: {e}")
            continue
    
    print(f"\nProcessing complete!                    ")
    
    # Calculate statistics
    coverage_percentage = (questions_with_coverage / total_questions * 100) if total_questions > 0 else 0
    
    # Print results
    print(f"\n{'-'*40}")
    print(f"COVERAGE STATISTICS FOR {dataset_name}")
    print(f"{'-'*40}")
    print(f"Total questions analyzed: {total_questions}")
    print(f"Questions with coverage (at least one True): {questions_with_coverage}")
    print(f"Questions without coverage (all False): {total_questions - questions_with_coverage}")
    print(f"Coverage percentage: {coverage_percentage:.2f}%")
    
    print(f"\n{'-'*40}")
    print(f"DISTRIBUTION OF TRUE VALUES PER QUESTION")
    print(f"{'-'*40}")
    
    # Sort by number of True values
    for true_count in sorted(true_count_distribution.keys()):
        count = true_count_distribution[true_count]
        percentage = (count / total_questions * 100) if total_questions > 0 else 0
        print(f"{true_count} True values: {count} questions ({percentage:.2f}%)")
    
    # Calculate average number of True values per question
    total_true_values = sum(k * v for k, v in true_count_distribution.items())
    avg_true_per_question = total_true_values / total_questions if total_questions > 0 else 0
    print(f"\nAverage True values per question: {avg_true_per_question:.2f}")
    
    return {
        'total_questions': total_questions,
        'questions_with_coverage': questions_with_coverage,
        'coverage_percentage': coverage_percentage,
        'true_count_distribution': dict(true_count_distribution),
        'avg_true_per_question': avg_true_per_question
    }

def main():
    # Analyze GSM8K dataset
    gsm8k_path = "/home/<USER>/BoN/experiments/bon_sft_simple/trained_eval"
    gsm8k_stats = analyze_coverage(gsm8k_path, "GSM8K")
    
    # Analyze MATH dataset
    math_path = "/home/<USER>/BoN/Sample_N/save/math_eval"
    math_stats = analyze_coverage(math_path, "MATH")
    
    # Print summary comparison
    print(f"\n{'='*60}")
    print(f"SUMMARY COMPARISON")
    print(f"{'='*60}")
    print(f"{'Dataset':<10} {'Total Q':<10} {'Coverage':<10} {'Coverage %':<12} {'Avg True/Q':<10}")
    print(f"{'-'*60}")
    print(f"{'GSM8K':<10} {gsm8k_stats['total_questions']:<10} {gsm8k_stats['questions_with_coverage']:<10} "
          f"{gsm8k_stats['coverage_percentage']:<12.2f} {gsm8k_stats['avg_true_per_question']:<10.2f}")
    print(f"{'MATH':<10} {math_stats['total_questions']:<10} {math_stats['questions_with_coverage']:<10} "
          f"{math_stats['coverage_percentage']:<12.2f} {math_stats['avg_true_per_question']:<10.2f}")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()