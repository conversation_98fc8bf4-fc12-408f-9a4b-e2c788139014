# Debug Configuration for Adaptive Budget CP

## Overview
This document provides debugging guidance for `adaptive_budget_cp.py`, which implements adaptive conformal prediction with difficulty-based bucketing.

## Key Components to Debug

### 1. DifficultyCalibrator Class
**Purpose**: Calibrates the conformal prediction thresholds based on difficulty buckets.

#### Critical Breakpoints:
- **Line 96**: `add_example()` - Start of example processing
- **Line 98**: After `_sample()` - Check generated sample quality
- **Line 99**: After `_p_correct()` - Verify verifier scores
- **Line 100**: Alpha calculation - Ensure proper log transformation
- **Line 101**: Bucket assignment - Verify difficulty classification
- **Line 142**: Quantile calculation in `finalize()`
- **Line 145**: Success rate calculation
- **Line 146**: N calculation for adaptive sampling

#### Debug Variables to Watch:
```python
# In add_example():
y          # Generated sample
p          # Verifier probability
alpha      # -log(p) score
g          # Bucket assignment ("easy", "mid", "hard")

# In finalize():
scores     # Sorted alpha scores per bucket
self.q[g]  # Quantile threshold per bucket
self.pi[g] # Success rate per bucket
self.N[g]  # Required samples per bucket
```

### 2. AdaptiveSampler Class
**Purpose**: Uses calibrated thresholds to generate prediction sets.

#### Critical Breakpoints:
- **Line 214**: Probe sample generation
- **Line 215**: Probe difficulty assessment
- **Line 216**: Bucket assignment for test example
- **Line 224**: Candidate generation loop
- **Line 225**: Candidate scoring
- **Line 228**: Acceptance/rejection decision
- **Line 254**: Batch processing with pre-generated samples

#### Debug Variables to Watch:
```python
# In predict_set():
probe      # Initial probe sample
p_probe    # Probe verifier score
g          # Assigned difficulty bucket
k          # Required number of samples (N[g])
cand       # Current candidate sample
score      # Candidate alpha score
retained   # List of accepted samples

# Performance metrics:
len(retained)  # Final prediction set size
elapsed        # Time taken
```

## Debug Scenarios

### Scenario 1: Calibration Phase
**Configuration**: "Debug Adaptive CP - Calibrate"
**Focus**: Understanding how difficulty buckets are populated

```python
# Key inspection points:
# 1. Model loading (lines 57-66)
# 2. Sample generation quality (line 98)
# 3. Verifier scoring accuracy (line 99)
# 4. Bucket distribution balance
# 5. Final threshold computation (line 142)
```

### Scenario 2: Prediction Phase
**Configuration**: "Debug Adaptive CP - Predict"
**Focus**: Adaptive sampling behavior

```python
# Key inspection points:
# 1. Probe-based difficulty estimation (lines 214-216)
# 2. Adaptive N selection (line 219)
# 3. Sample acceptance rates per bucket
# 4. Final prediction set sizes
```

### Scenario 3: Pre-generated Samples
**Configuration**: "Debug Adaptive CP - Calibrate/Predict with Samples"
**Focus**: Efficiency with pre-computed samples

```python
# Key inspection points:
# 1. YAML data loading (lines 363-369)
# 2. Sample selection strategy (line 107)
# 3. Batch processing efficiency
```

## Common Issues to Debug

### 1. Empty Buckets
**Symptom**: ValueError in `finalize()` - "No calibration scores for bucket"
**Debug**: Check bucket thresholds (EASY_TH, MID_TH) and verifier behavior

### 2. Poor Verifier Scores
**Symptom**: All samples assigned to "hard" bucket
**Debug**: Inspect `_p_correct()` outputs and verifier model performance

### 3. Memory Issues
**Symptom**: CUDA OOM errors
**Debug**: Monitor GPU memory usage, consider smaller batch sizes

### 4. Slow Performance
**Symptom**: Long calibration/prediction times
**Debug**: Profile model inference times, consider using pre-generated samples

## Environment Variables for Debug

```bash
# Enable detailed logging
export PYTHONPATH="${PYTHONPATH}:."
export CUDA_VISIBLE_DEVICES="0"

# For memory debugging
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:512"

# For reproducibility
export PYTHONHASHSEED=42
```

## Useful Debug Commands

### Quick Calibration Test
```bash
python adaptive_budget_cp.py calibrate \
  --folder experiments/bon_sft_simple/base_samples \
  --n 5 \
  --out debug_calib.json \
  --use_samples
```

### Quick Prediction Test
```bash
python adaptive_budget_cp.py predict \
  --calib debug_calib.json \
  --folder test_yaml \
  --n 3 \
  --out debug_sets.jsonl \
  --use_samples
```

## Expected Behavior

### Healthy Calibration:
- Balanced bucket distribution (not all samples in one bucket)
- Reasonable quantile thresholds (q values)
- Success rates (pi) close to target coverage (1-alpha = 0.9)
- Adaptive N values that make sense (easy < mid < hard)

### Healthy Prediction:
- Probe samples correctly classified into buckets
- Prediction set sizes vary based on difficulty
- Non-empty prediction sets for most examples
- Reasonable inference times

## Troubleshooting Tips

1. **Start Small**: Use `--n 3` for initial debugging
2. **Check Data**: Verify YAML files have expected structure
3. **Monitor Resources**: Watch GPU memory and CPU usage
4. **Log Analysis**: Check `adaptive_cp.log` for detailed traces
5. **Model Compatibility**: Ensure policy and verifier models are compatible
