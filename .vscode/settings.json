{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=100"],
    "python.sortImports.args": ["--profile", "black"],
    
    // Debug settings
    "debug.console.fontSize": 14,
    "debug.console.wordWrap": true,
    "debug.inlineValues": true,
    "debug.showBreakpointsInOverviewRuler": true,
    "debug.toolBarLocation": "floating",
    
    // Python debugging
    "python.debugging.console": "integratedTerminal",
    "python.debugging.justMyCode": false,
    "python.debugging.showReturnValue": true,
    "python.debugging.redirectOutput": true,
    
    // File associations for YAML data files
    "files.associations": {
        "*.yaml": "yaml",
        "*.yml": "yaml",
        "*.jsonl": "jsonlines"
    },
    
    // Editor settings for better debugging experience
    "editor.rulers": [88, 100],
    "editor.wordWrap": "on",
    "editor.minimap.enabled": true,
    "editor.minimap.showSlider": "always",
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": true,
    
    // Terminal settings
    "terminal.integrated.fontSize": 14,
    "terminal.integrated.scrollback": 10000,
    
    // Specific settings for adaptive_budget_cp.py debugging
    "python.analysis.extraPaths": ["./"],
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    
    // Logging and output
    "python.logging.level": "debug",
    
    // Git settings
    "git.ignoreLimitWarning": true,
    
    // Search settings
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/venv": true,
        "**/env": true,
        "**/.git": true,
        "**/checkpoints": true,
        "**/experiments": true,
        "**/data": true
    }
}
