{"version": "0.2.0", "configurations": [{"name": "Debug BoN Pipeline: Generate", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/bon_pipeline.py", "console": "integratedTerminal", "args": ["generate", "--model", "gpt2-medium", "--dataset", "gsm8k", "--split", "test[:100]", "--n", "8", "--out", "${workspaceFolder}/data/bon.jsonl"]}, {"name": "Debug BoN Pipeline: Finetune", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/bon_pipeline.py", "console": "integratedTerminal", "args": ["finetune", "--base_model", "gpt2-medium", "--train_file", "${workspaceFolder}/data/bon.jsonl", "--output_dir", "${workspaceFolder}/checkpoints/bon_ft", "--lr", "5e-5", "--epochs", "3"]}, {"name": "Debug BoN Pipeline: Evaluate", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/bon_pipeline.py", "console": "integratedTerminal", "args": ["evaluate", "--model", "${workspaceFolder}/checkpoints/bon_ft", "--dataset", "gsm8k", "--split", "test[:100]", "--n", "8"]}, {"name": "Debug Adaptive CP - Calibrate", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/adaptive_budget_cp.py", "args": ["calibrate", "--folder", "experiments/bon_sft_simple/base_samples", "--n", "10", "--policy", "deepseek-ai/MathCoder-7B-18k-sft", "--verifier", "Qwen/Qwen2.5-Math-7B-PRM800K", "--out", "debug_calib.json", "--T", "1.0"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CUDA_VISIBLE_DEVICES": "0"}, "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Adaptive CP - Calibrate with Samples", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/adaptive_budget_cp.py", "args": ["calibrate", "--folder", "experiments/bon_sft_simple/base_samples", "--n", "5", "--policy", "deepseek-ai/MathCoder-7B-18k-sft", "--verifier", "Qwen/Qwen2.5-Math-7B-PRM800K", "--out", "debug_calib_samples.json", "--use_samples"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CUDA_VISIBLE_DEVICES": "0"}, "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Adaptive CP - Predict", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/adaptive_budget_cp.py", "args": ["predict", "--calib", "calib.json", "--folder", "test_yaml", "--policy", "deepseek-ai/MathCoder-7B-18k-sft", "--verifier", "Qwen/Qwen2.5-Math-7B-PRM800K", "--out", "debug_sets.jsonl", "--n", "3", "--probe_T", "1.0", "--gen_T", "0.8"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CUDA_VISIBLE_DEVICES": "0"}, "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Adaptive CP - Predict with <PERSON><PERSON>", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/adaptive_budget_cp.py", "args": ["predict", "--calib", "calib.json", "--folder", "test_yaml", "--policy", "deepseek-ai/MathCoder-7B-18k-sft", "--verifier", "Qwen/Qwen2.5-Math-7B-PRM800K", "--out", "debug_sets_samples.jsonl", "--n", "3", "--use_samples"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CUDA_VISIBLE_DEVICES": "0"}, "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Adaptive CP - Interactive", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/adaptive_budget_cp.py", "args": [], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CUDA_VISIBLE_DEVICES": "0"}, "justMyCode": false, "stopOnEntry": true}, {"name": "Debug Adaptive CP - Custom Args", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/adaptive_budget_cp.py", "args": ["${input:command}", "${input:customArgs}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CUDA_VISIBLE_DEVICES": "0"}, "justMyCode": false, "stopOnEntry": false}], "inputs": [{"id": "command", "description": "Select command (calibrate or predict)", "type": "pickString", "options": ["calibrate", "predict"]}, {"id": "customArgs", "description": "Enter custom arguments", "type": "promptString", "default": "--help"}]}