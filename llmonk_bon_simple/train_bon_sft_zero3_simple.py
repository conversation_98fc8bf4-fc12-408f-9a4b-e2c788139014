#!/usr/bin/env python3
"""
Simplified BoN-SFT training with DeepSpeed ZeRO-3.
Works directly with DeepSpeed without pydra.
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import deepspeed
from tqdm import tqdm
import logging
from pathlib import Path
import random
import numpy as np
import sys
import os
import json
import argparse

# Fix transformers tensor parallel bug
import transformers.modeling_utils
if not hasattr(transformers.modeling_utils, 'ALL_PARALLEL_STYLES') or transformers.modeling_utils.ALL_PARALLEL_STYLES is None:
    transformers.modeling_utils.ALL_PARALLEL_STYLES = ['colwise', 'rowwise', 'colwise_rep', None]

# Add llmonk to path
sys.path.append("/home/<USER>/BoN/Sample_N/large_language_monkeys")
from llmonk.utils import load_yaml, save_yaml

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_deepspeed_config(args):
    """Create DeepSpeed ZeRO-3 configuration"""
    ds_config = {
        "fp16": {
            "enabled": True,
            "loss_scale": 0,
            "loss_scale_window": 1000,
            "initial_scale_power": 16,
            "hysteresis": 2,
            "min_loss_scale": 1
        },
        "optimizer": {
            "type": "AdamW",
            "params": {
                "lr": args.learning_rate,
                "betas": [0.9, 0.999],
                "eps": 1e-8,
                "weight_decay": 0.01
            }
        },
        "scheduler": {
            "type": "WarmupLR",
            "params": {
                "warmup_min_lr": 0,
                "warmup_max_lr": args.learning_rate,
                "warmup_num_steps": 100
            }
        },
        "zero_optimization": {
            "stage": 3,
            "offload_optimizer": {
                "device": "cpu",
                "pin_memory": True
            },
            "offload_param": {
                "device": "cpu", 
                "pin_memory": True
            },
            "overlap_comm": True,
            "contiguous_gradients": True,
            "sub_group_size": 1e9,
            "reduce_bucket_size": "auto",
            "stage3_prefetch_bucket_size": "auto",
            "stage3_param_persistence_threshold": "auto",
            "stage3_max_live_parameters": 1e9,
            "stage3_max_reuse_distance": 1e9,
            "stage3_gather_16bit_weights_on_model_save": True
        },
        "gradient_accumulation_steps": args.gradient_accumulation_steps,
        "gradient_clipping": 1.0,
        "train_batch_size": args.batch_size * torch.cuda.device_count() * args.gradient_accumulation_steps,
        "train_micro_batch_size_per_gpu": args.batch_size,
        "wall_clock_breakdown": False
    }
    
    return ds_config


def compute_log_prob(model, tokenizer, prompt, response, device, max_seq_length):
    """Compute log probability of response given prompt"""
    # Tokenize
    prompt_tokens = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=max_seq_length // 2)
    response_tokens = tokenizer(response, return_tensors="pt", truncation=True, max_length=max_seq_length // 2, add_special_tokens=False)
    
    # Concatenate
    input_ids = torch.cat([prompt_tokens['input_ids'], response_tokens['input_ids']], dim=1).to(device)
    attention_mask = torch.ones_like(input_ids)
    
    # Create labels
    labels = input_ids.clone()
    prompt_length = prompt_tokens['input_ids'].shape[1]
    labels[:, :prompt_length] = -100
    
    # Forward
    outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
    
    # Count response tokens
    response_length = (labels != -100).sum()
    
    if response_length == 0:
        return torch.tensor(0.0, device=device, requires_grad=True)
    
    return -outputs.loss * response_length


def bon_sft_loss(model, tokenizer, data_item, args, device):
    """Compute BoN-SFT loss"""
    prompt = data_item['prompt']
    samples = data_item['samples'][:args.N]
    is_corrects = data_item['is_corrects'][:args.N]
    gt_answer = data_item['gt_answer']
    
    rewards = torch.tensor(is_corrects, dtype=torch.float32, device=device)
    
    # Find best sample
    best_idx = torch.argmax(rewards)
    best_sample = samples[best_idx.item()]
    r_bon = rewards[best_idx]
    
    # Expert response
    expert_response = gt_answer
    r_expert = torch.tensor(1.0, device=device)
    
    # Compute log probs
    expert_log_prob = compute_log_prob(model, tokenizer, prompt, expert_response, device, args.max_seq_length)
    bon_log_prob = compute_log_prob(model, tokenizer, prompt, best_sample, device, args.max_seq_length)
    
    # Compute Q values
    with torch.no_grad():
        q_expert = torch.sigmoid(r_expert - rewards).mean()
        q_bon = torch.sigmoid(r_bon - rewards).mean()
    
    # Loss
    f_expert = expert_log_prob + args.lambda_N * q_expert
    f_bon = bon_log_prob + args.lambda_N * q_bon
    
    return -(f_expert - f_bon)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model', type=str, required=True)
    parser.add_argument('--save_dir', type=str, required=True)
    parser.add_argument('--samples_dir', type=str, required=True)
    parser.add_argument('--num_epochs', type=int, default=3)
    parser.add_argument('--batch_size', type=int, default=1)
    parser.add_argument('--gradient_accumulation_steps', type=int, default=16)
    parser.add_argument('--learning_rate', type=float, default=5e-5)
    parser.add_argument('--max_seq_length', type=int, default=512)
    parser.add_argument('--N', type=int, default=10)
    parser.add_argument('--lambda_N', type=float, default=1.0)
    parser.add_argument('--log_interval', type=int, default=10)
    parser.add_argument('--local_rank', type=int, default=-1)
    args = parser.parse_args()
    
    # Setup
    local_rank = int(os.environ.get("LOCAL_RANK", 0))
    torch.cuda.set_device(local_rank)
    device = torch.device("cuda", local_rank)
    
    # Initialize DeepSpeed distributed
    deepspeed.init_distributed()
    world_size = torch.distributed.get_world_size()
    
    if local_rank == 0:
        logger.info(f"Starting BoN-SFT training with DeepSpeed ZeRO-3")
        logger.info(f"Model: {args.model}")
        logger.info(f"World size: {world_size}")
        Path(args.save_dir).mkdir(exist_ok=True, parents=True)
    
    # Set seed
    torch.manual_seed(42 + local_rank)
    random.seed(42 + local_rank)
    np.random.seed(42 + local_rank)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(args.model, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Create DeepSpeed config
    ds_config = create_deepspeed_config(args)
    
    # Load model
    if local_rank == 0:
        logger.info("Loading model with DeepSpeed ZeRO-3...")
    
    model = AutoModelForCausalLM.from_pretrained(
        args.model,
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
    
    # Enable gradient checkpointing
    model.gradient_checkpointing_enable()
    
    # Initialize DeepSpeed
    model_engine, optimizer, _, _ = deepspeed.initialize(
        model=model,
        config=ds_config,
        model_parameters=model.parameters()
    )
    
    if local_rank == 0:
        logger.info("Model loaded and distributed!")
    
    # Load data
    samples_dir = Path(args.samples_dir)
    all_samples = []
    
    for yaml_file in sorted(samples_dir.glob("*.yaml")):
        data = load_yaml(yaml_file)
        if 'is_corrects' in data and len(data.get('samples', [])) > 0:
            all_samples.append(data)
    
    if local_rank == 0:
        logger.info(f"Loaded {len(all_samples)} samples")
    
    # Training
    global_step = 0
    
    for epoch in range(args.num_epochs):
        epoch_loss = 0.0
        num_batches = 0
        
        # Shuffle
        random.Random(42 + epoch).shuffle(all_samples)
        
        # Distribute samples
        samples_per_gpu = len(all_samples) // world_size
        start_idx = local_rank * samples_per_gpu
        end_idx = start_idx + samples_per_gpu if local_rank < world_size - 1 else len(all_samples)
        gpu_samples = all_samples[start_idx:end_idx]
        
        # Create batches
        batches = []
        for i in range(0, len(gpu_samples), args.batch_size):
            batches.append(gpu_samples[i:i + args.batch_size])
        
        # Progress bar
        if local_rank == 0:
            progress_bar = tqdm(batches, desc=f"Epoch {epoch+1}/{args.num_epochs}")
        else:
            progress_bar = batches
        
        for batch in progress_bar:
            # Compute loss
            batch_loss = 0.0
            valid_items = 0
            
            for data_item in batch:
                try:
                    loss = bon_sft_loss(model_engine, tokenizer, data_item, args, device)
                    if not torch.isnan(loss) and not torch.isinf(loss):
                        batch_loss += loss
                        valid_items += 1
                except Exception as e:
                    if local_rank == 0:
                        logger.warning(f"Error: {e}")
                    continue
            
            if valid_items == 0:
                continue
            
            # Average loss
            batch_loss = batch_loss / valid_items
            epoch_loss += batch_loss.item()
            num_batches += 1
            
            # Backward
            model_engine.backward(batch_loss)
            
            # Step
            model_engine.step()
            global_step += 1
            
            # Log
            if local_rank == 0 and global_step % args.log_interval == 0:
                avg_loss = epoch_loss / num_batches if num_batches > 0 else 0
                progress_bar.set_postfix({'loss': f"{avg_loss:.4f}"})
                logger.info(f"Step {global_step}, Loss: {avg_loss:.4f}")
    
    # Save final model
    if local_rank == 0:
        final_dir = Path(args.save_dir) / "final_model"
        model_engine.save_checkpoint(final_dir)
        
        # Save tokenizer
        tokenizer.save_pretrained(final_dir)
        logger.info(f"Training completed! Model saved to {final_dir}")


if __name__ == "__main__":
    main()