#!/usr/bin/env python3
"""
BoN-SFT training script that uses existing llmonk infrastructure.
Only adds the training logic - everything else uses llmonk directly.
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from torch.optim import Adam<PERSON>
from peft import LoraConfig, get_peft_model, TaskType
import pydra
from tqdm import tqdm
import logging
from pathlib import Path
import random
import numpy as np
import sys
import os

# Fix transformers tensor parallel bug
import transformers.modeling_utils
if not hasattr(transformers.modeling_utils, 'ALL_PARALLEL_STYLES') or transformers.modeling_utils.ALL_PARALLEL_STYLES is None:
    transformers.modeling_utils.ALL_PARALLEL_STYLES = ['colwise', 'rowwise', 'colwise_rep', None]

# Add llmonk to path to use its utilities
sys.path.append("/home/<USER>/BoN/Sample_N/large_language_monkeys")

from llmonk.utils import load_yaml, save_yaml, Config, REQUIRED
from llmonk.generate.vllm_utils import vllm_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BoNSFTConfig(Config):
    """Configuration for BoN-SFT training"""
    model = REQUIRED
    save_dir = REQUIRED
    samples_dir = REQUIRED  # Directory with evaluated samples from llmonk
    
    # Training hyperparameters
    learning_rate = 5e-5
    batch_size = 4
    gradient_accumulation_steps = 4
    num_epochs = 3
    warmup_steps = 100
    max_seq_length = 1024  # Reduced to save memory
    
    # BoN-SFT specific
    N = 10  # Number of samples for Best-of-N
    lambda_N = 1.0  # Lambda parameter for BoN-SFT loss
    
    # LoRA configuration
    use_lora = True
    lora_r = 16
    lora_alpha = 32
    lora_dropout = 0.1
    
    # Hardware
    device = "cuda"
    fp16 = True
    seed = 42
    
    # Checkpointing
    save_every = 1
    log_interval = 10
    
    def finalize(self):
        self.save_dir = Path(self.save_dir)
        self.save_dir.mkdir(exist_ok=True, parents=True)
        self.samples_dir = Path(self.samples_dir)


def compute_log_prob(model, tokenizer, prompt, response, device, max_seq_length):
    """Compute log probability of response given prompt WITH GRADIENTS"""
    # Ensure model is in training mode to compute gradients
    was_training = model.training
    model.train()
    
    # Tokenize prompt and response separately for better control
    prompt_tokens = tokenizer(
        prompt,
        return_tensors="pt",
        truncation=True,
        max_length=max_seq_length // 2
    )
    
    response_tokens = tokenizer(
        response,
        return_tensors="pt",
        truncation=True,
        max_length=max_seq_length // 2,
        add_special_tokens=False
    )
    
    # Concatenate tokens
    input_ids = torch.cat([
        prompt_tokens['input_ids'],
        response_tokens['input_ids']
    ], dim=1).to(device)
    
    attention_mask = torch.ones_like(input_ids)
    
    # Create labels (mask prompt part with -100)
    labels = input_ids.clone()
    prompt_length = prompt_tokens['input_ids'].shape[1]
    labels[:, :prompt_length] = -100  # Ignore prompt tokens in loss
    
    # Forward pass WITH gradients (no torch.no_grad()!)
    outputs = model(
        input_ids=input_ids,
        attention_mask=attention_mask,
        labels=labels
    )
    
    # Count response tokens
    response_length = (labels != -100).sum()
    
    # Restore original training mode
    model.train(was_training)
    
    if response_length == 0:
        return torch.tensor(0.0, device=device, requires_grad=True)
    
    # Return total log probability of response
    # Note: outputs.loss is negative log likelihood, so we negate it
    return -outputs.loss * response_length


def bon_sft_loss(model, tokenizer, data_item, config):
    """Compute BoN-SFT loss using pre-generated samples from llmonk"""
    prompt = data_item['prompt']
    samples = data_item['samples']
    is_corrects = data_item['is_corrects']
    gt_answer = data_item['gt_answer']
    
    # Use is_corrects as rewards (already computed by llmonk evaluation)
    # Limit to N samples
    if len(samples) > config.N:
        samples = samples[:config.N]
        is_corrects = is_corrects[:config.N]
    
    rewards = torch.tensor(is_corrects, dtype=torch.float32, device=config.device)
    
    # Find best sample
    best_idx = torch.argmax(rewards)
    best_sample = samples[best_idx.item()]  # Convert to Python int
    r_bon = rewards[best_idx]
    
    # Expert response is the ground truth
    expert_response = gt_answer
    r_expert = torch.tensor(1.0, device=config.device)  # Expert response is always correct
    
    # IMPORTANT: Keep model in training mode for gradient computation
    model.train()
    
    # Compute log probabilities WITH gradients
    expert_log_prob = compute_log_prob(
        model, tokenizer, prompt, expert_response, 
        config.device, config.max_seq_length
    )
    
    bon_log_prob = compute_log_prob(
        model, tokenizer, prompt, best_sample,
        config.device, config.max_seq_length
    )
    
    # Compute Q values (these should be detached - no gradients through rewards)
    with torch.no_grad():
        q_expert = torch.sigmoid(r_expert - rewards).mean()
        q_bon = torch.sigmoid(r_bon - rewards).mean()
    
    # Compute f terms
    f_expert = expert_log_prob + config.lambda_N * q_expert
    f_bon = bon_log_prob + config.lambda_N * q_bon
    
    # Loss: negative because we want to maximize (f_expert - f_bon)
    loss = -(f_expert - f_bon)
    
    return loss


@pydra.main(BoNSFTConfig)
def main(config: BoNSFTConfig):
    logger.info(f"Starting BoN-SFT training")
    logger.info(f"Model: {config.model}")
    logger.info(f"Samples directory: {config.samples_dir}")
    
    # Set random seed
    torch.manual_seed(config.seed)
    random.seed(config.seed)
    np.random.seed(config.seed)
    
    # Load model and tokenizer
    logger.info(f"Loading model: {config.model}")
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        config.model,
        torch_dtype=torch.float16 if config.fp16 else torch.float32,
        low_cpu_mem_usage=True,
        trust_remote_code=True
    )
    
    # Move model to device
    if config.device == "cuda" and torch.cuda.is_available():
        model = model.cuda()
        logger.info(f"Model moved to CUDA device")
    
    # Enable gradient checkpointing to save memory
    model.gradient_checkpointing_enable()
    logger.info("Gradient checkpointing enabled")
    
    # Apply LoRA if enabled
    if config.use_lora:
        logger.info("Applying LoRA configuration...")
        lora_config = LoraConfig(
            r=config.lora_r,
            lora_alpha=config.lora_alpha,
            lora_dropout=config.lora_dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        )
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
    
    # Set model to training mode
    model.train()
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(config.model, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load all evaluated samples from llmonk
    logger.info(f"Loading samples from {config.samples_dir}")
    yaml_files = sorted(config.samples_dir.glob("*.yaml"))
    all_samples = []
    
    for yaml_file in yaml_files:
        data = load_yaml(yaml_file)
        # Only use samples that have been evaluated (have is_corrects field)
        if 'is_corrects' in data and len(data.get('samples', [])) > 0:
            all_samples.append(data)
    
    logger.info(f"Loaded {len(all_samples)} evaluated samples")
    
    if len(all_samples) == 0:
        logger.error("No evaluated samples found!")
        return
    
    # Create optimizer
    optimizer = AdamW(model.parameters(), lr=config.learning_rate)
    
    # Training loop
    global_step = 0
    
    for epoch in range(config.num_epochs):
        epoch_loss = 0.0
        num_batches = 0
        
        # Shuffle training data
        random.shuffle(all_samples)
        
        # Create batches
        batches = []
        for i in range(0, len(all_samples), config.batch_size):
            batches.append(all_samples[i:i + config.batch_size])
        
        # Progress bar
        progress_bar = tqdm(batches, desc=f"Epoch {epoch+1}/{config.num_epochs}")
        
        # Zero gradients at start
        optimizer.zero_grad()
        
        for batch_idx, batch in enumerate(progress_bar):
            # Compute loss for batch
            batch_loss = 0.0
            valid_items = 0
            
            for data_item in batch:
                try:
                    loss = bon_sft_loss(model, tokenizer, data_item, config)
                    if not torch.isnan(loss) and not torch.isinf(loss):
                        batch_loss += loss
                        valid_items += 1
                    else:
                        logger.warning(f"Skipping item due to nan/inf loss")
                except Exception as e:
                    logger.warning(f"Error computing loss for item: {e}")
                    continue
            
            if valid_items == 0:
                logger.warning("No valid items in batch, skipping")
                continue
            
            # Average loss
            batch_loss = batch_loss / valid_items
            epoch_loss += batch_loss.item()
            num_batches += 1
            
            # Scale loss for gradient accumulation
            batch_loss = batch_loss / config.gradient_accumulation_steps
            batch_loss.backward()
            
            # Gradient accumulation
            if (batch_idx + 1) % config.gradient_accumulation_steps == 0 or batch_idx == len(batches) - 1:
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                optimizer.zero_grad()
                global_step += 1
                
                # Logging
                if global_step % config.log_interval == 0:
                    avg_loss = epoch_loss / num_batches if num_batches > 0 else 0
                    progress_bar.set_postfix({'loss': f"{avg_loss:.4f}"})
                    logger.info(f"Step {global_step}, Loss: {avg_loss:.4f}")
                    
                    # Log gradient norms for debugging
                    if global_step % (config.log_interval * 10) == 0:
                        total_norm = 0
                        param_count = 0
                        for name, param in model.named_parameters():
                            if param.grad is not None:
                                param_norm = param.grad.data.norm(2).item()
                                total_norm += param_norm ** 2
                                param_count += 1
                        if param_count > 0:
                            total_norm = total_norm ** 0.5
                            logger.info(f"Gradient norm: {total_norm:.4f} over {param_count} parameters")
        
        # Save checkpoint
        if (epoch + 1) % config.save_every == 0:
            checkpoint_dir = config.save_dir / f"checkpoint_epoch_{epoch+1}"
            checkpoint_dir.mkdir(exist_ok=True, parents=True)
            
            # Save model and tokenizer
            if config.use_lora:
                model.save_pretrained(checkpoint_dir)
            else:
                model.save_pretrained(checkpoint_dir)
            tokenizer.save_pretrained(checkpoint_dir)
            
            # Save optimizer state
            optimizer_path = checkpoint_dir / "optimizer.pt"
            torch.save({
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch + 1,
                'global_step': global_step,
            }, optimizer_path)
            
            logger.info(f"Saved checkpoint to {checkpoint_dir}")
    
    # Save final model
    final_path = config.save_dir / "final_model"
    final_path.mkdir(exist_ok=True, parents=True)
    if config.use_lora:
        model.save_pretrained(final_path)
        # Save the base model name for later merging
        with open(final_path / "base_model.txt", "w") as f:
            f.write(config.model)
    else:
        model.save_pretrained(final_path)
    tokenizer.save_pretrained(final_path)
    logger.info(f"Training completed. Final model saved to {final_path}")
    
    # Save training summary
    summary = {
        'model': config.model,
        'samples_dir': str(config.samples_dir),
        'num_epochs': config.num_epochs,
        'final_step': global_step,
        'num_samples_trained': len(all_samples),
        'config': config.__dict__
    }
    save_yaml(config.save_dir / "training_summary.yaml", summary)


if __name__ == "__main__":
    main()