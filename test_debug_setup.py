#!/usr/bin/env python3
"""
Test script to validate debug setup for adaptive_budget_cp.py
Run this to ensure all dependencies and configurations are working.
"""

import sys
import os
import json
import yaml
import pathlib
import logging
from typing import Dict, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required imports work."""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        print(f"  CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"  CUDA device: {torch.cuda.get_device_name()}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        print("✓ Transformers")
    except ImportError as e:
        print(f"✗ Transformers import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    try:
        from tqdm import tqdm
        print("✓ tqdm")
    except ImportError as e:
        print(f"✗ tqdm import failed: {e}")
        return False
    
    return True

def test_adaptive_cp_import():
    """Test that adaptive_budget_cp.py can be imported."""
    print("\nTesting adaptive_budget_cp import...")
    
    try:
        import adaptive_budget_cp
        print("✓ adaptive_budget_cp.py imported successfully")
        
        # Test class imports
        from adaptive_budget_cp import DifficultyCalibrator, AdaptiveSampler
        print("✓ Main classes imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ adaptive_budget_cp import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Error importing adaptive_budget_cp: {e}")
        return False

def test_debug_config():
    """Test that debug configuration files exist and are valid."""
    print("\nTesting debug configuration...")
    
    # Check launch.json
    launch_json_path = ".vscode/launch.json"
    if os.path.exists(launch_json_path):
        try:
            with open(launch_json_path, 'r') as f:
                launch_config = json.load(f)
            
            # Check for adaptive CP configurations
            config_names = [config['name'] for config in launch_config['configurations']]
            adaptive_configs = [name for name in config_names if 'Adaptive CP' in name]
            
            print(f"✓ launch.json exists with {len(adaptive_configs)} Adaptive CP configurations:")
            for name in adaptive_configs:
                print(f"  - {name}")
                
        except json.JSONDecodeError as e:
            print(f"✗ launch.json is invalid JSON: {e}")
            return False
        except Exception as e:
            print(f"✗ Error reading launch.json: {e}")
            return False
    else:
        print("✗ launch.json not found")
        return False
    
    # Check settings.json
    settings_json_path = ".vscode/settings.json"
    if os.path.exists(settings_json_path):
        try:
            with open(settings_json_path, 'r') as f:
                settings = json.load(f)
            print("✓ settings.json exists and is valid")
        except json.JSONDecodeError as e:
            print(f"✗ settings.json is invalid JSON: {e}")
            return False
    else:
        print("⚠ settings.json not found (optional)")
    
    return True

def test_sample_data_structure():
    """Test sample data structure for debugging."""
    print("\nTesting sample data structure...")
    
    # Create a minimal test YAML file
    test_data = {
        'prompt': 'Solve this math problem: ',
        'question': 'What is 2 + 2?',
        'samples': [
            'The answer is 4.',
            'Two plus two equals four.',
            '2 + 2 = 4'
        ],
        'ground_truth': '4'
    }
    
    test_dir = pathlib.Path("test_debug_data")
    test_dir.mkdir(exist_ok=True)
    
    test_file = test_dir / "sample_001.yaml"
    try:
        with open(test_file, 'w') as f:
            yaml.dump(test_data, f, default_flow_style=False)
        print(f"✓ Created test YAML file: {test_file}")
        
        # Verify it can be read back
        with open(test_file, 'r') as f:
            loaded_data = yaml.safe_load(f)
        
        required_keys = ['prompt', 'question', 'samples']
        missing_keys = [key for key in required_keys if key not in loaded_data]
        
        if missing_keys:
            print(f"✗ Test YAML missing required keys: {missing_keys}")
            return False
        else:
            print("✓ Test YAML structure is valid")
            
    except Exception as e:
        print(f"✗ Error creating/reading test YAML: {e}")
        return False
    
    return True

def test_logging_setup():
    """Test logging configuration."""
    print("\nTesting logging setup...")
    
    try:
        # Test basic logging
        logger = logging.getLogger("test_debug")
        logger.setLevel(logging.DEBUG)
        
        # Create a test handler
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        logger.info("Test log message")
        print("✓ Logging setup works")
        
        return True
    except Exception as e:
        print(f"✗ Logging setup failed: {e}")
        return False

def create_debug_summary():
    """Create a summary of debug setup."""
    print("\n" + "="*50)
    print("DEBUG SETUP SUMMARY")
    print("="*50)
    
    summary = {
        "debug_configurations": [
            "Debug Adaptive CP - Calibrate",
            "Debug Adaptive CP - Calibrate with Samples", 
            "Debug Adaptive CP - Predict",
            "Debug Adaptive CP - Predict with Samples",
            "Debug Adaptive CP - Interactive",
            "Debug Adaptive CP - Custom Args"
        ],
        "key_breakpoints": {
            "DifficultyCalibrator": [96, 98, 99, 100, 101, 142, 145, 146],
            "AdaptiveSampler": [214, 215, 216, 224, 225, 228, 254]
        },
        "debug_files": [
            ".vscode/launch.json",
            ".vscode/settings.json", 
            "debug_adaptive_cp.md",
            "test_debug_setup.py"
        ],
        "test_commands": [
            "python test_debug_setup.py",
            "python adaptive_budget_cp.py --help",
            "python adaptive_budget_cp.py calibrate --help",
            "python adaptive_budget_cp.py predict --help"
        ]
    }
    
    print("Available Debug Configurations:")
    for config in summary["debug_configurations"]:
        print(f"  - {config}")
    
    print("\nKey Breakpoint Lines:")
    for class_name, lines in summary["key_breakpoints"].items():
        print(f"  {class_name}: {lines}")
    
    print("\nDebug Files Created:")
    for file_path in summary["debug_files"]:
        exists = "✓" if os.path.exists(file_path) else "✗"
        print(f"  {exists} {file_path}")
    
    print("\nTest Commands:")
    for cmd in summary["test_commands"]:
        print(f"  {cmd}")

def main():
    """Run all debug setup tests."""
    print("ADAPTIVE BUDGET CP DEBUG SETUP TEST")
    print("="*40)
    
    tests = [
        test_imports,
        test_adaptive_cp_import,
        test_debug_config,
        test_sample_data_structure,
        test_logging_setup
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    create_debug_summary()
    
    print(f"\n{'='*50}")
    print(f"RESULTS: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All tests passed! Debug setup is ready.")
        print("\nTo start debugging:")
        print("1. Open VS Code")
        print("2. Go to Run and Debug (Ctrl+Shift+D)")
        print("3. Select an 'Adaptive CP' configuration")
        print("4. Set breakpoints in adaptive_budget_cp.py")
        print("5. Press F5 to start debugging")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
