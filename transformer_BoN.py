from transformers import pipeline, AutoTokenizer
from trl import AutoModelForCausalLMWithValueHead
from trl.extras import BestOfNSampler

# Setup reward model and tokenizer
reward_pipe = pipeline("sentiment-analysis", model=reward_model, device=device)
tokenizer = AutoTokenizer.from_pretrained(model_name)

# Create BoN sampler
def queries_to_scores(list_of_strings):
    return [output["score"] for output in reward_pipe(list_of_strings)]

best_of_n = BestOfNSampler(model, tokenizer, queries_to_scores, sample_size=8)

# Generate with BoN
results = best_of_n.generate(query_tensors, device=device, **gen_kwargs)