from __future__ import annotations
import math, json, time, pathlib, logging, yaml
from typing import List, Tuple, Dict
from tqdm import tqdm
import random

import numpy as np
import torch
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    AutoModelForSequenceClassification,
)

__all__ = [
    "DifficultyCalibrator",
    "AdaptiveSampler",
]

#‑‑‑ CONFIG --------------------------------------------------------------------------------

EASY_TH   = 0.60   # p_ver probe > 0.60 ⇒ “easy” bucket
MID_TH    = 0.30   # 0.30 < p ≤ 0.60   ⇒ “mid”
ALPHA     = 0.10   # target risk level 1‑α  (90 % coverage)
MAX_GEN   = 512    # safety cap on new tokens per generation

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('adaptive_cp.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

#‑‑‑ CALIBRATION ---------------------------------------------------------------------------

class DifficultyCalibrator:
    """Split‑CP calibration that stores per‑bucket thresholds + success rates."""

    def __init__(
        self,
        policy_id: str,
        verifier_id: str,
        device: str = "cuda",
        alpha: float = ALPHA,
        easy_th: float = EASY_TH,
        mid_th: float = MID_TH,
    ) -> None:
        self.alpha = alpha
        self.easy_th, self.mid_th = easy_th, mid_th
        self.device = device if torch.cuda.is_available() else "cpu"

        # load models once
        self.pol_tok  = AutoTokenizer.from_pretrained(policy_id, trust_remote_code=True)
        self.policy   = AutoModelForCausalLM.from_pretrained(
            policy_id, device_map="auto", torch_dtype=torch.bfloat16
        ).eval()

        self.ver_tok  = AutoTokenizer.from_pretrained(verifier_id, trust_remote_code=True)
        self.verifier = AutoModelForSequenceClassification.from_pretrained(
            verifier_id, device_map="auto", torch_dtype=torch.bfloat16,
            trust_remote_code=True,
        ).eval()

        # running containers
        self._scores: Dict[str, List[float]] = {"easy": [], "mid": [], "hard": []}
        self.q:  Dict[str, float] = {}
        self.pi: Dict[str, float] = {}
        self.N:  Dict[str, int]   = {}

    # ‑‑‑ helpers ‑‑‑
    def _sample(self, prompt: str, T: float = 1.0) -> str:
        inp = self.pol_tok(prompt, return_tensors="pt").to(self.device)
        out = self.policy.generate(**inp, temperature=T, max_new_tokens=MAX_GEN)
        return self.pol_tok.decode(out[0, inp["input_ids"].size(1):], skip_special_tokens=True)

    def _p_correct(self, prompt: str, ans: str) -> float:
        text = prompt + "\n\n" + ans
        inp  = self.ver_tok(text, return_tensors="pt").to(self.device)
        with torch.no_grad():
            logit = self.verifier(**inp).logits[0, 1]
        return torch.sigmoid(logit).item()

    def _bucket(self, p: float) -> str:
        if p > self.easy_th:
            return "easy"
        if p > self.mid_th:
            return "mid"
        return "hard"

    # ‑‑‑ public API ‑‑‑
    @torch.inference_mode()
    def add_example(self, x: str, T: float = 1.0) -> None:
        """Draw one sample ỹ from policy |x, compute α, drop it in a bucket."""
        y = self._sample(x, T)
        p = self._p_correct(x, y)
        alpha = -math.log(max(p, 1e-12))          # avoid log(0)
        g = self._bucket(p)
        self._scores[g].append(alpha)
        logger.debug(f"Added example to bucket '{g}' with alpha={alpha:.4f}, p={p:.4f}")

    def add_example_with_samples(self, x: str, samples: List[str]) -> None:
        """Add calibration data using pre-generated samples."""
        select_sample = random.choice(samples)

        p = self._p_correct(x, select_sample)
        alpha = -math.log(max(p, 1e-12))
        g = self._bucket(p)
        self._scores[g].append(alpha)
        logger.debug(f"Added sample to bucket '{g}' with alpha={alpha:.4f}, p={p:.4f}")
    
    def add_batch(self, examples: List[str], T: float = 1.0) -> None:
        """Process multiple examples."""
        logger.info(f"Processing {len(examples)} examples")
        
        for x in tqdm(examples, desc="Calibration examples"):
            try:
                self.add_example(x, T)
            except Exception as e:
                logger.error(f"Error processing example: {e}")
    
    def add_batch_with_samples(self, examples: List[Tuple[str, List[str]]]) -> None:
        """Process multiple examples with pre-generated samples."""
        logger.info(f"Processing {len(examples)} examples with pre-generated samples")
        
        for x, samples in tqdm(examples, desc="Calibration examples"):
            try:
                self.add_example_with_samples(x, samples)
            except Exception as e:
                logger.error(f"Error processing example: {e}")

    def finalize(self) -> None:
        """Compute q_g,  π̂_g  and N_g  after all examples have been added."""
        logger.info("Finalizing calibration...")
        for g, scores in self._scores.items():
            if len(scores) == 0:
                raise ValueError(f"No calibration scores for bucket {g}")
            scores = np.sort(np.asarray(scores))
            self.q[g] = float(np.quantile(scores, 1.0 - self.alpha))
            # success indicator = 1{α ≤ q_g}
            success = (scores <= self.q[g]).astype(np.int8)
            self.pi[g] = float((success.sum() + 1) / (len(success) + 1))   # CP lower‑bound
            self.N[g]  = math.ceil(math.log(self.alpha) / math.log(max(1e-4, 1-self.pi[g])))
            logger.info(f"Bucket '{g}': q={self.q[g]:.4f}, pi={self.pi[g]:.4f}, N={self.N[g]}")

    def save(self, path: str | pathlib.Path):
        data = {"alpha": self.alpha,
                "easy_th": self.easy_th,
                "mid_th": self.mid_th,
                "q": self.q, "pi": self.pi, "N": self.N}
        with open(path, "w") as f:
            json.dump(data, f, indent=2)
        logger.info(f"Calibration saved to {path}")

    # convenience
    @classmethod
    def load_static(cls, calib_json: str | pathlib.Path):
        with open(calib_json) as f:
            data = json.load(f)
        obj = cls.__new__(cls)           # bypass __init__
        obj.alpha = data["alpha"] ; obj.easy_th = data["easy_th"] ; obj.mid_th = data["mid_th"]
        obj.q, obj.pi, obj.N = data["q"], data["pi"], data["N"]
        return obj

#‑‑‑ INFERENCE -----------------------------------------------------------------------------

class AdaptiveSampler:
    """Wraps a policy + verifier with adaptive‑N conformal filtering."""

    def __init__(self, calib: DifficultyCalibrator | str, policy_id: str | None = None,
                 verifier_id: str | None = None, device: str = "cuda"):
        # load static params
        if isinstance(calib, (str, pathlib.Path)):
            calib = DifficultyCalibrator.load_static(calib)
        self.q, self.N = calib.q, calib.N
        self.bucket = calib._bucket
        self.device = calib.device if policy_id is None else device

        # models (share w/ calibrator if same object)
        if isinstance(calib, DifficultyCalibrator):
            self.pol_tok, self.policy = calib.pol_tok, calib.policy
            self.ver_tok, self.verifier = calib.ver_tok, calib.verifier
        else:
            assert policy_id and verifier_id, "Need model ids when loading from json"
            self.pol_tok  = AutoTokenizer.from_pretrained(policy_id, trust_remote_code=True)
            self.policy   = AutoModelForCausalLM.from_pretrained(
                policy_id, device_map="auto", torch_dtype=torch.bfloat16).eval()
            self.ver_tok  = AutoTokenizer.from_pretrained(verifier_id, trust_remote_code=True)
            self.verifier = AutoModelForSequenceClassification.from_pretrained(
                verifier_id, device_map="auto", torch_dtype=torch.bfloat16,
                trust_remote_code=True).eval()

    @torch.inference_mode()
    def _sample(self, prompt: str, T=0.8) -> str:
        inp = self.pol_tok(prompt, return_tensors="pt").to(self.device)
        out = self.policy.generate(**inp, temperature=T, max_new_tokens=MAX_GEN)
        return self.pol_tok.decode(out[0, inp["input_ids"].size(1):], skip_special_tokens=True)

    @torch.inference_mode()
    def _p_correct(self, prompt: str, ans: str) -> float:
        text = prompt + "\n\n" + ans
        inp  = self.ver_tok(text, return_tensors="pt").to(self.device)
        logit = self.verifier(**inp).logits[0, 1]
        return torch.sigmoid(logit).item()

    def predict_set(self, prompt: str, probe_T: float = 1.0, gen_T: float = 0.8) -> List[str]:
        """Return conformal prediction set 𝒮(prompt) with group‑wise guarantee."""
        start_time = time.time()
        
        # 1) quick probe → difficulty bucket
        probe = self._sample(prompt, T=probe_T)
        p_probe = self._p_correct(prompt, probe)
        g = self.bucket(p_probe)
        logger.debug(f"Probe assigned to bucket '{g}' with p={p_probe:.4f}")

        k = self.N[g]
        retained: List[str] = []
        scores_tried = []
        
        for i in range(k):
            cand = self._sample(prompt, T=gen_T)
            score = -math.log(max(self._p_correct(prompt, cand), 1e-12))
            scores_tried.append(score)
            
            if score <= self.q[g]:
                retained.append(cand)
                logger.debug(f"Sample {i+1}/{k} accepted with score={score:.4f}")
            else:
                logger.debug(f"Sample {i+1}/{k} rejected with score={score:.4f}")
        
        elapsed = time.time() - start_time
        logger.info(f"Prediction set size: {len(retained)}/{k} (bucket={g}, time={elapsed:.2f}s)")
        return retained
    
    def predict_set_with_samples(self, prompt: str, samples: List[str]) -> List[str]:
        """Return conformal prediction set using pre-generated samples."""
        start_time = time.time()
        
        # Use first sample as probe for difficulty estimation
        if not samples:
            logger.warning("No samples provided")
            return []
            
        probe = samples[0]
        p_probe = self._p_correct(prompt, probe)
        g = self.bucket(p_probe)
        logger.debug(f"Probe assigned to bucket '{g}' with p={p_probe:.4f}")
        
        # Filter all samples based on the bucket's threshold
        retained = []
        for i, sample in enumerate(samples):
            score = -math.log(max(self._p_correct(prompt, sample), 1e-12))
            
            if score <= self.q[g]:
                retained.append(sample)
                logger.debug(f"Sample {i+1}/{len(samples)} accepted with score={score:.4f}")
            else:
                logger.debug(f"Sample {i+1}/{len(samples)} rejected with score={score:.4f}")
        
        elapsed = time.time() - start_time
        logger.info(f"Prediction set size: {len(retained)}/{len(samples)} (bucket={g}, time={elapsed:.2f}s)")
        return retained
    
    def predict_batch(self, prompts: List[str], probe_T: float = 1.0, gen_T: float = 0.8) -> List[List[str]]:
        """Generate prediction sets for multiple prompts."""
        logger.info(f"Generating prediction sets for {len(prompts)} prompts")
        results = []
        
        for prompt in tqdm(prompts, desc="Prediction sets"):
            try:
                result = self.predict_set(prompt, probe_T, gen_T)
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing prompt: {e}")
                results.append([])
        
        return results
    
    def predict_batch_with_samples(self, data: List[Tuple[str, List[str]]]) -> List[List[str]]:
        """Generate prediction sets using pre-generated samples."""
        logger.info(f"Generating prediction sets for {len(data)} prompts with pre-generated samples")
        results = []
        
        for prompt, samples in tqdm(data, desc="Prediction sets"):
            try:
                result = self.predict_set_with_samples(prompt, samples)
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing prompt: {e}")
                results.append([])
        
        return results


#--- MAIN EXECUTION -----------------------------------------------------------------------

def main():
    import argparse
    
    P = argparse.ArgumentParser("Adaptive‑Budget CP (REV‑B)")
    sub = P.add_subparsers(dest="cmd", required=True)

    # Calibrate subcommand
    c = sub.add_parser("calibrate", help="Run calibration on dataset")
    c.add_argument("--folder",  default="experiments/bon_sft_simple/base_samples",
                help="Path to folder containing calibration data")
    c.add_argument("--n",       type=int, default=50,
                help="Number of calibration examples to use")
    c.add_argument("--policy",  default="deepseek-ai/MathCoder-7B-18k-sft",
                help="Policy model HF ID")
    c.add_argument("--verifier",default="Qwen/Qwen2.5-Math-7B-PRM800K",
                help="Verifier model HF ID")
    c.add_argument("--out",     default="calib.json",
                help="Output path for calibration results")
    c.add_argument("--T",       type=float, default=1.0,
                help="Temperature for calibration sampling")
    c.add_argument("--use_samples", action="store_true",
                help="Use pre-generated samples from YAML files")


    # Predict subcommand
    p = sub.add_parser("predict", help="Generate predictions with calibrated model")
    p.add_argument("--calib",  default="calib.json",
                help="Path to calibration file")
    p.add_argument("--folder", default="test_yaml",
                help="Path to folder containing test data")
    p.add_argument("--policy", default="deepseek-ai/MathCoder-7B-18k-sft",
                help="Policy model HF ID")
    p.add_argument("--verifier", default="Qwen/Qwen2.5-Math-7B-PRM800K",
                help="Verifier model HF ID")
    p.add_argument("--out",    default="sets.jsonl",
                help="Output path for prediction sets")
    p.add_argument("--n",      type=int, default=None,
                help="Number of test examples to use (None = all)")
    p.add_argument("--probe_T",type=float, default=1.0,
                help="Temperature for the initial probe sample")
    p.add_argument("--gen_T",  type=float, default=0.8,
                help="Temperature for subsequent generation")
    p.add_argument("--use_samples", action="store_true",
                help="Use pre-generated samples from YAML files")


    args = P.parse_args()

    if args.cmd == "calibrate":
        logger.info("=== Running Calibration ===")
        
        # Load calibration data from folder
        data_files = list(pathlib.Path(args.folder).glob("*.yaml"))
        if not data_files:
            data_files = list(pathlib.Path(args.folder).glob("*.yml"))
        
        if args.use_samples:
            # Load examples with pre-generated samples
            calib_examples = []
            for file_path in sorted(data_files):
                if len(calib_examples) >= args.n:
                    break
                    
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
                    
                    prompt = data['prompt']
                    samples = data['samples']
                    
                    calib_examples.append((prompt, samples))
            
            calib_examples = calib_examples[:args.n]
            logger.info(f"Loaded {len(calib_examples)} calibration examples with pre-generated samples")
            
            # Initialize calibrator
            calibrator = DifficultyCalibrator(
                policy_id=args.policy,
                verifier_id=args.verifier,
                device="cuda" if torch.cuda.is_available() else "cpu"
            )
            
            # Run calibration with samples
            calibrator.add_batch_with_samples(calib_examples)
        else:
            # Original mode: generate samples during calibration
            calib_examples = []
            for file_path in sorted(data_files):
                if len(calib_examples) >= args.n:
                    break
                    
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
                    
                    prompt = data['prompt'] + data['question']
                    
                    calib_examples.append(prompt)
            
            calib_examples = calib_examples[:args.n]
            logger.info(f"Loaded {len(calib_examples)} calibration examples")
            
            # Initialize calibrator
            calibrator = DifficultyCalibrator(
                policy_id=args.policy,
                verifier_id=args.verifier,
                device="cuda" if torch.cuda.is_available() else "cpu"
            )
            
            # Run calibration
            calibrator.add_batch(calib_examples, T=args.T)
        
        # Finalize and save
        calibrator.finalize()
        calibrator.save(args.out)
        logger.info(f"Calibration saved to {args.out}")
        
    elif args.cmd == "predict":
        logger.info("=== Running Prediction ===")
        
        # Load test data from folder
        data_files = list(pathlib.Path(args.folder).glob("*.yaml"))
        if not data_files:
            data_files = list(pathlib.Path(args.folder).glob("*.yml"))
        
        if args.use_samples:
            # Load test data with pre-generated samples
            test_data_with_samples = []
            test_data = []
            for file_path in sorted(data_files):
                if args.n and len(test_data_with_samples) >= args.n:
                    break
                    
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
                    
                    prompt = data['prompt'] + data['question']
                    samples = data['samples']
                    
                    test_data_with_samples.append((prompt, samples))
                    test_data.append(data)
            
            if args.n:
                test_data_with_samples = test_data_with_samples[:args.n]
                test_data = test_data[:args.n]
                
            logger.info(f"Loaded {len(test_data_with_samples)} test examples with pre-generated samples")
            
            # Initialize sampler
            sampler = AdaptiveSampler(
                calib=args.calib,
                policy_id=args.policy,
                verifier_id=args.verifier
            )
            
            # Generate predictions using pre-generated samples
            prediction_sets = sampler.predict_batch_with_samples(test_data_with_samples)
        else:
            # Original mode: generate samples during prediction
            test_prompts = []
            test_data = []
            for file_path in sorted(data_files):
                if args.n and len(test_prompts) >= args.n:
                    break
                    
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
                    
                    prompt = data['prompt'] + data['question']
                    test_prompts.append(prompt)
                    test_data.append(data)
            
            if args.n:
                test_prompts = test_prompts[:args.n]
                test_data = test_data[:args.n]
                
            logger.info(f"Loaded {len(test_prompts)} test examples")
            
            # Initialize sampler
            sampler = AdaptiveSampler(
                calib=args.calib,
                policy_id=args.policy,
                verifier_id=args.verifier
            )
            
            # Generate predictions
            prediction_sets = sampler.predict_batch(
                test_prompts,
                probe_T=args.probe_T,
                gen_T=args.gen_T
            )
        
        # Save results
        with open(args.out, 'w') as f:
            for i, preds in enumerate(prediction_sets):
                result = {
                    'prediction_set': preds,
                    'set_size': len(preds)
                }
                # Include all fields from original data
                if i < len(test_data):
                    for key, value in test_data[i].items():
                        if key not in result:
                            result[key] = value
                
                f.write(json.dumps(result) + '\n')
        
        # Summary statistics
        sizes = [len(p) for p in prediction_sets]
        logger.info(f"Predictions saved to {args.out}")
        logger.info(f"Average set size: {np.mean(sizes):.2f} ± {np.std(sizes):.2f}")
        logger.info(f"Empty sets: {sum(1 for s in sizes if s == 0)} / {len(sizes)}")
        logger.info(f"Coverage by bucket: easy={sum(1 for p in prediction_sets if len(p) > 0)}/{len(prediction_sets)}")


if __name__ == "__main__":
    main()
