#!/usr/bin/env python3
"""
Generate GSM-8K samples with a LoRA-tuned Llama-3 in vLLM ≥ 0.5

– Formats *prompt*, *samples*, *gt_answer* exactly like the original
  (non-fine-tuned) pipeline.
– Builds a fresh 2-shot context for **each** test item (same RNG behaviour).
– Dumps YAML with the same options as llmonk.utils.save_yaml, so
  byte-for-byte identical except for the model’s answers.

Usage (examples)
----------------
# plain base model, no LoRA
python gsm8k_lora_gen.py --output-dir runs/plain

# base + LoRA folder (will read base_model.txt automatically)
python gsm8k_lora_gen.py \
    --lora-path /path/to/final_model \
    --output-dir runs/lora
"""
import os
import random
import argparse
from pathlib import Path
from datasets import load_dataset
from tqdm import tqdm
import yaml

from vllm import LLM, SamplingParams
from vllm.lora.request import LoRARequest


# --------------------------------------------------------------------------- #
# YAML helper (same behaviour as llmonk.utils.save_yaml)
# --------------------------------------------------------------------------- #
def save_yaml(path, obj) -> None:
    with open(path, "w") as f:
        yaml.safe_dump(
            obj,
            f,
            sort_keys=False,           # keep key order
            allow_unicode=True,
            default_flow_style=False,  # block-style lists
        )


# --------------------------------------------------------------------------- #
def build_few_shot_prompt(item) -> str:
    """Return 2-shot prompt for *this* test item (matches legacy format)."""
    pieces = [
        f"Question: {shot['question']}\nAnswer: {shot['answer']}\n\n"
        for shot in item["few_shot_items"]
    ]
    return "".join(pieces)


# --------------------------------------------------------------------------- #
def main() -> None:
    p = argparse.ArgumentParser()
    p.add_argument("--base-model",
                   default="meta-llama/Meta-Llama-3-8B-Instruct",
                   help="HF hub name or local path to base weights.")
    p.add_argument("--lora-path",
                   help="Folder with adapter_config.json & adapter_model.*. "
                        "If omitted, run the base model only.")
    p.add_argument("--output-dir", required=True,
                   help="Where to write <id>.yaml files.")
    p.add_argument("--num-samples", type=int, default=10)
    p.add_argument("--limit", type=int, default=100)
    p.add_argument("--seed", type=int, default=0)
    p.add_argument("--temperature", type=float, default=0.6)
    p.add_argument("--max-tokens", type=int, default=1024)
    p.add_argument("--gpu", default="0")
    args = p.parse_args()

    os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu
    random.seed(args.seed)

    # -------------------- vLLM initialisation --------------------- #
    print("Loading base model:", args.base_model)
    llm = LLM(
        model=args.base_model,
        enable_lora=bool(args.lora_path),
        max_lora_rank=32,  # Increased to be safe with rank-32 adapters
        trust_remote_code=True,
        tensor_parallel_size=1,
        dtype="auto",  # Let vLLM decide the dtype to match the model
        max_model_len=2048,
    )

    lora_request = None
    if args.lora_path:
        print("Loading LoRA adapter:", args.lora_path)
        lora_request = LoRARequest(
            lora_name="adapter",
            lora_int_id=1,
            lora_local_path=args.lora_path,
        )

    # -------------------- dataset prep ---------------------------- #
    test_ds  = list(load_dataset("gsm8k", "main", split="test"))
    train_ds = list(load_dataset("gsm8k", "main", split="train"))

    # Each test item gets its own 2-shot context
    for i, d in enumerate(test_ds):
        d["id"] = i
        d["few_shot_items"] = random.sample(train_ds, 2)

    random.shuffle(test_ds)
    test_ds = test_ds[: args.limit]

    # -------------------- output dir ------------------------------ #
    out_dir = Path(args.output_dir).expanduser().resolve()
    out_dir.mkdir(parents=True, exist_ok=True)

    print(f"Generating {args.num_samples} samples "
          f"for {len(test_ds)} GSM-8K problems …")

    # shared sampling params
    sparams = SamplingParams(
        n=args.num_samples,
        temperature=args.temperature,
        top_p=0.95,
        max_tokens=args.max_tokens,
        stop=["Q:", "Question:"],
    )

    # -------------------- main loop ------------------------------- #
    for item in tqdm(test_ds):
        outfile = out_dir / f"{item['id']}.yaml"
        if outfile.exists():
            continue

        prompt = (
            build_few_shot_prompt(item)
            + f"Question: {item['question']}\nAnswer:"
        )

        try:
            outputs = llm.generate(
                prompts=[prompt],
                sampling_params=sparams,
                lora_request=lora_request,
            )
        except Exception as e:
            print(f"Error generating for item {item['id']}: {e}")
            # Fallback: generate without LoRA to see if base model works
            print("Trying without LoRA...")
            outputs = llm.generate(
                prompts=[prompt],
                sampling_params=sparams,
            )
        samples = [o.text for o in outputs[0].outputs]

        result = {
            "prompt": prompt,
            "question": item["question"],
            "samples": samples,
            "gt_answer": item["answer"],
        }
        save_yaml(outfile, result)

    print("✓ Generation complete →", out_dir)


# --------------------------------------------------------------------------- #
if __name__ == "__main__":
    main()



# #!/usr/bin/env python3
# """
# Generate GSM-8K samples with a LoRA-tuned Llama using vLLM 0.5+
# Replicates the formatting & sampling logic of the original non-finetuned pipeline.
# """
# import os, sys, argparse, random, yaml
# from pathlib import Path
# from tqdm import tqdm
# from datasets import load_dataset
# from vllm import LLM, SamplingParams
# from vllm.lora.request import LoRARequest

# # -------------------------------------------------------------------
# # helper: identical to llmonk.utils.save_yaml
# # -------------------------------------------------------------------
# def save_yaml(path, obj):
#     with open(path, "w") as f:
#         yaml.safe_dump(
#             obj, f,
#             sort_keys=False,
#             allow_unicode=True,
#             default_flow_style=False,
#         )

# # -------------------------------------------------------------------
# def build_few_shot_prompt(train_ds, k=2):
#     """Sample k items and format exactly like the old code."""
#     shots = random.sample(train_ds, k)
#     parts = [
#         f"Question: {ex['question']}\nAnswer: {ex['answer']}\n\n"
#         for ex in shots
#     ]
#     return "".join(parts)

# # -------------------------------------------------------------------
# def main():
#     p = argparse.ArgumentParser()
#     p.add_argument("--base-model", default="meta-llama/Meta-Llama-3-8B-Instruct")
#     p.add_argument("--lora-path",
#                    default="/home/<USER>/BoN/experiments/bon_sft_simple/training/final_model")
#     p.add_argument("--output-dir",
#                    default="/home/<USER>/BoN/experiments/bon_sft_simple/trained_samples_vllm")
#     p.add_argument("--num-samples", type=int, default=10)
#     p.add_argument("--limit", type=int, default=100)
#     p.add_argument("--temperature", type=float, default=0.6)
#     p.add_argument("--max-tokens", type=int, default=1024)
#     p.add_argument("--gpu", default="0")
#     p.add_argument("--seed", type=int, default=0)
#     args = p.parse_args()

#     os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu
#     random.seed(args.seed)

#     # ---------------------- vLLM + LoRA -----------------------------
#     llm = LLM(
#         model=args.base_model,
#         enable_lora=True,
#         max_lora_rank=32,
#         trust_remote_code=True,
#         tensor_parallel_size=1,
#         dtype="float16",
#         max_model_len=2048,
#     )
#     lora_req = LoRARequest(
#         lora_name="adapter",
#         lora_int_id=1,
#         lora_local_path=args.lora_path,
#     )

#     # ---------------------- dataset prep ---------------------------
#     test_ds  = list(load_dataset("gsm8k", "main", split="test"))
#     train_ds = list(load_dataset("gsm8k", "main", split="train"))

#     random.shuffle(test_ds)                # same as old script
#     test_ds = test_ds[: args.limit]

#     out_dir = Path(args.output_dir)
#     out_dir.mkdir(parents=True, exist_ok=True)

#     # ---------------------- generation loop ------------------------
#     print(
#         f"Base model   : {args.base_model}\n"
#         f"LoRA adapter : {args.lora_path}\n"
#         f"Samples/item : {args.num_samples}\n"
#         f"Problems     : {len(test_ds)}"
#     )

#     sampling_params = SamplingParams(
#         n=args.num_samples,
#         temperature=args.temperature,
#         top_p=0.95,
#         max_tokens=args.max_tokens,
#         stop=["Q:", "Question:"],
#     )

#     for idx, item in enumerate(tqdm(test_ds)):
#         outfile = out_dir / f"{idx}.yaml"
#         if outfile.exists():
#             continue

#         few_shot_prompt = build_few_shot_prompt(train_ds, k=2)
#         prompt = f"{few_shot_prompt}Question: {item['question']}\nAnswer:"

#         outputs = llm.generate(
#             prompts=[prompt],
#             sampling_params=sampling_params,
#             lora_request=lora_req,
#         )
#         samples = [o.text for o in outputs[0].outputs]

#         result = {
#             "prompt": prompt,
#             "question": item["question"],
#             "samples": samples,
#             "gt_answer": item["answer"],
#         }
#         save_yaml(outfile, result)

#     print(f"✓ Done. Files written to {out_dir}")

# # -------------------------------------------------------------------
# if __name__ == "__main__":
#     main()
