#!/bin/bash

# Working BoN-SFT workflow with proper LoRA support
# Uses a custom script that properly handles vLLM 0.5.4 LoRA loading

export SAVE_DIR="/home/<USER>/BoN/experiments/bon_sft_simple"
export MODEL="meta-llama/Meta-Llama-3-8B-Instruct"
export CUDA_VISIBLE_DEVICES=2,3

echo "=== BoN-SFT Workflow ==="
echo "Correct order: Generate → Evaluate → Train → Generate → Evaluate"
echo ""

# Step 1: Generate samples with BASE MODEL using ORIGINAL llmonk
echo "Step 1: Generating samples with BASE model..."
cd /home/<USER>/BoN/Sample_N/large_language_monkeys
python llmonk/generate/gsm8k.py \
    model=$MODEL \
    save_dir=$SAVE_DIR/base_samples \
    num_samples=10 \
    limit=100 \
    --list vllm_args --disable-log-requests list-- \
    --list stop_strings Q: Question: list--

# Step 2: Evaluate BASE MODEL samples using ORIGINAL llmonk
echo "Step 2: Evaluating base model samples..."
python llmonk/evaluate/math_datasets.py \
    samples_dir=$SAVE_DIR/base_samples \
    save_dir=$SAVE_DIR/base_eval \
    dset=gsm8k

# Step 3: Train with BoN-SFT using evaluated samples (ONLY NEW PART)
echo "Step 3: Training with BoN-SFT..."
cd /home/<USER>/BoN
python llmonk_bon_simple/train_bon_sft.py \
        model=$MODEL \
        save_dir=$SAVE_DIR/training \
        samples_dir=$SAVE_DIR/base_eval \
        num_epochs=3 \
        N=10 \
        lambda_N=0.1 \
        learning_rate=2e-5 \
        batch_size=4 \
        gradient_accumulation_steps=16 \
        max_seq_length=1024 \
        warmup_steps=50 \
        lora_r=32 \
        lora_alpha=64 \
        save_every=1 \
        log_interval=5

# Step 4: Generate NEW samples with TRAINED MODEL using custom LoRA script
echo "Step 4: Generating samples with TRAINED model (LoRA adapter)..."
cd /home/<USER>/BoN
python generate_with_lora_vllm054.py \
    --base-model $MODEL \
    --lora-path $SAVE_DIR/training/final_model \
    --output-dir $SAVE_DIR/trained_samples \
    --num-samples 10 \
    --limit 100 \
    --gpu "2,3"

# Step 5: Evaluate TRAINED MODEL samples using ORIGINAL llmonk
echo "Step 5: Evaluating trained model samples..."
cd /home/<USER>/BoN/Sample_N/large_language_monkeys
python llmonk/evaluate/math_datasets.py \
    samples_dir=$SAVE_DIR/trained_samples \
    save_dir=$SAVE_DIR/trained_eval \
    dset=gsm8k

# Step 6: Compare results
echo "Step 6: Comparing base vs trained model..."
echo ""
echo "Base model results in: $SAVE_DIR/base_eval"
echo "Trained model results in: $SAVE_DIR/trained_eval"
echo ""

# Show quick summary if eval files exist
if [ -f "$SAVE_DIR/base_eval/summary.yaml" ] && [ -f "$SAVE_DIR/trained_eval/summary.yaml" ]; then
    echo "=== Results Summary ==="
    echo "Base model:"
    grep "accuracy" "$SAVE_DIR/base_eval/summary.yaml" || true
    echo ""
    echo "Trained model (with LoRA):"
    grep "accuracy" "$SAVE_DIR/trained_eval/summary.yaml" || true
fi

echo ""
echo "Done! Successfully completed BoN-SFT workflow with LoRA adapter."